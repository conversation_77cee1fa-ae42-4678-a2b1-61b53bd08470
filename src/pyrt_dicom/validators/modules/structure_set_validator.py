"""Structure Set Module DICOM validation - PS3.3 C.8.8.5.

Validates Structure Set Module compliance with DICOM PS3.3 standard,
including complex conditional logic for sequences and nested requirements.
"""

from typing import Union, TYPE_CHECKING
from pydicom import Dataset
from .base_validator import <PERSON>Validator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.rt_enums import ROIGenerationAlgorithm

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule


class StructureSetValidator(BaseValidator):
    """Validator for DICOM Structure Set Module (PS3.3 C.8.8.5).

    Provides comprehensive validation of Structure Set Module requirements including:
    - Core element validation (Type 1/2 requirements)
    - Complex sequence validation with nested conditional logic
    - Cross-field dependency validation
    - Enumerated value validation
    - DICOM compliance guidance for end users
    """

    @staticmethod
    def validate_required_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1 and Type 2 required elements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        result = ValidationResult()

        # Structure Set Label (3006,0002) - Type 1
        if 'StructureSetLabel' not in data or not data.StructureSetLabel:
            result.add_error(
                "Structure Set Label (3006,0002) is required (Type 1). "
                "This user-defined label identifies the Structure Set."
            )

        # Structure Set Date (3006,0008) - Type 2 (required but may be empty)
        if 'StructureSetDate' not in data:
            result.add_error(
                "Structure Set Date (3006,0008) is required (Type 2). "
                "This date indicates when the Structure Set content was last modified. "
                "May be empty if date is unknown."
            )

        # Structure Set Time (3006,0009) - Type 2 (required but may be empty)
        if 'StructureSetTime' not in data:
            result.add_error(
                "Structure Set Time (3006,0009) is required (Type 2). "
                "This time indicates when the Structure Set content was last modified. "
                "May be empty if time is unknown."
            )

        return result

    @staticmethod
    def validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1C and Type 2C conditional requirements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        result = ValidationResult()

        # Validate Type 1C conditional elements in ROI items
        if 'StructureSetROISequence' in data:
            structure_set_roi_seq = data.StructureSetROISequence
            for i, roi_item in enumerate(structure_set_roi_seq):
                StructureSetValidator._validate_roi_conditional_elements(roi_item, i, result)

        return result

    @staticmethod
    def validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate enumerated value constraints.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        result = ValidationResult()

        # Validate ROI Generation Algorithm in Structure Set ROI Sequence
        if 'StructureSetROISequence' in data:
            structure_set_roi_seq = data.StructureSetROISequence
            for i, roi_item in enumerate(structure_set_roi_seq):
                if 'ROIGenerationAlgorithm' in roi_item:
                    roi_gen_algorithm = roi_item.ROIGenerationAlgorithm
                    if roi_gen_algorithm:
                        valid_algorithms = [alg.value for alg in ROIGenerationAlgorithm]
                        BaseValidator.validate_enumerated_value(
                            roi_gen_algorithm, valid_algorithms,
                            f"ROI Generation Algorithm (3006,0036) in Structure Set ROI Sequence item {i}",
                            result
                        )

        return result

    @staticmethod
    def validate_sequence_structures(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate sequence structure requirements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        result = ValidationResult()

        # Referenced Frame of Reference Sequence validation
        if 'ReferencedFrameOfReferenceSequence' in data:
            ref_frame_seq = data.ReferencedFrameOfReferenceSequence
            for i, frame_item in enumerate(ref_frame_seq):
                # Frame of Reference UID is Type 1 within the sequence
                if 'FrameOfReferenceUID' not in frame_item or not frame_item.FrameOfReferenceUID:
                    result.add_error(
                        f"Referenced Frame of Reference Sequence item {i}: "
                        "Frame of Reference UID (0020,0052) is required"
                    )

                # Validate RT Referenced Study Sequence if present
                if 'RTReferencedStudySequence' in frame_item:
                    rt_ref_study_seq = frame_item.RTReferencedStudySequence
                    for j, study_item in enumerate(rt_ref_study_seq):
                        if 'ReferencedSOPClassUID' not in study_item or not study_item.ReferencedSOPClassUID:
                            result.add_error(
                                f"RT Referenced Study Sequence item {j} in frame {i}: "
                                "Referenced SOP Class UID (0008,1150) is required"
                            )
                        if 'ReferencedSOPInstanceUID' not in study_item or not study_item.ReferencedSOPInstanceUID:
                            result.add_error(
                                f"RT Referenced Study Sequence item {j} in frame {i}: "
                                "Referenced SOP Instance UID (0008,1155) is required"
                            )

                        # Validate RT Referenced Series Sequence (Type 1 if study is present)
                        if 'RTReferencedSeriesSequence' not in study_item or not study_item.RTReferencedSeriesSequence:
                            result.add_error(
                                f"RT Referenced Study Sequence item {j} in frame {i}: "
                                "RT Referenced Series Sequence (3006,0014) is required"
                            )
                        else:
                            rt_ref_series_seq = study_item.RTReferencedSeriesSequence
                            for k, series_item in enumerate(rt_ref_series_seq):
                                if 'SeriesInstanceUID' not in series_item or not series_item.SeriesInstanceUID:
                                    result.add_error(
                                        f"RT Referenced Series Sequence item {k} in study {j}, frame {i}: "
                                        "Series Instance UID (0020,000E) is required"
                                    )

                                # Validate Contour Image Sequence (Type 1 if series is present)
                                if 'ContourImageSequence' not in series_item or not series_item.ContourImageSequence:
                                    result.add_error(
                                        f"RT Referenced Series Sequence item {k} in study {j}, frame {i}: "
                                        "Contour Image Sequence (3006,0016) is required"
                                    )
                                else:
                                    contour_image_seq = series_item.ContourImageSequence
                                    for l, image_item in enumerate(contour_image_seq):
                                        if 'ReferencedSOPClassUID' not in image_item or not image_item.ReferencedSOPClassUID:
                                            result.add_error(
                                                f"Contour Image Sequence item {l} in series {k}, study {j}, frame {i}: "
                                                "Referenced SOP Class UID (0008,1150) is required"
                                            )
                                        if 'ReferencedSOPInstanceUID' not in image_item or not image_item.ReferencedSOPInstanceUID:
                                            result.add_error(
                                                f"Contour Image Sequence item {l} in series {k}, study {j}, frame {i}: "
                                                "Referenced SOP Instance UID (0008,1155) is required"
                                            )

        # Structure Set ROI Sequence validation
        if 'StructureSetROISequence' in data:
            structure_set_roi_seq = data.StructureSetROISequence
            for i, roi_item in enumerate(structure_set_roi_seq):
                StructureSetValidator._validate_roi_item(roi_item, i, result)

                # Validate ROI Number uniqueness within structure set (only check once per pair)
                if 'ROINumber' in roi_item:
                    roi_number = roi_item.ROINumber
                    for j, other_roi in enumerate(structure_set_roi_seq):
                        if j > i and 'ROINumber' in other_roi and other_roi.ROINumber == roi_number:
                            result.add_error(
                                f"Structure Set ROI Sequence: ROI Number {roi_number} is not unique. "
                                f"ROI Number (3006,0022) must be unique within the Structure Set. "
                                f"Found duplicate in items {i} and {j}."
                            )
                            break  # Only report first duplicate found

        # Source Series Information Sequence validation
        if 'SourceSeriesInformationSequence' in data:
            source_series_seq = data.SourceSeriesInformationSequence
            for i, series_item in enumerate(source_series_seq):
                # Required elements for source series information
                required_fields = [
                    ('Modality', '0008,0060'),
                    ('SeriesDate', '0008,0021'),
                    ('SeriesTime', '0008,0031'),
                    ('SeriesDescription', '0008,103E'),
                    ('SeriesInstanceUID', '0020,000E'),
                    ('SeriesNumber', '0020,0011')
                ]

                for field_name, tag in required_fields:
                    if field_name not in series_item or not series_item[field_name]:
                        result.add_error(
                            f"Source Series Information Sequence item {i}: "
                            f"{field_name} ({tag}) is required"
                        )

        # Predecessor Structure Set Sequence validation
        if 'PredecessorStructureSetSequence' in data:
            predecessor_seq = data.PredecessorStructureSetSequence
            for i, pred_item in enumerate(predecessor_seq):
                if 'ReferencedSOPClassUID' not in pred_item or not pred_item.ReferencedSOPClassUID:
                    result.add_error(
                        f"Predecessor Structure Set Sequence item {i}: "
                        "Referenced SOP Class UID (0008,1150) is required. "
                        "This identifies the SOP Class of the predecessor Structure Set."
                    )
                if 'ReferencedSOPInstanceUID' not in pred_item or not pred_item.ReferencedSOPInstanceUID:
                    result.add_error(
                        f"Predecessor Structure Set Sequence item {i}: "
                        "Referenced SOP Instance UID (0008,1155) is required. "
                        "This identifies the SOP Instance of the predecessor Structure Set."
                    )

        return result

    @staticmethod
    def validate_cross_field_dependencies(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate cross-field dependencies and logical consistency.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for cross-field dependency violations
        """
        result = ValidationResult()

        # Validate that ROI Frame of Reference UIDs reference actual frames
        ref_frame_seq = data.ReferencedFrameOfReferenceSequence if 'ReferencedFrameOfReferenceSequence' in data else []
        structure_set_roi_seq = data.StructureSetROISequence if 'StructureSetROISequence' in data else []

        # Collect all available Frame of Reference UIDs
        available_frame_uids = set()
        for frame_item in ref_frame_seq:
            if 'FrameOfReferenceUID' in frame_item:
                frame_uid = frame_item.FrameOfReferenceUID
                if frame_uid:
                    available_frame_uids.add(frame_uid)

        # Check that each ROI references a valid Frame of Reference
        for i, roi_item in enumerate(structure_set_roi_seq):
            if 'ReferencedFrameOfReferenceUID' in roi_item:
                referenced_frame_uid = roi_item.ReferencedFrameOfReferenceUID
                if referenced_frame_uid and referenced_frame_uid not in available_frame_uids:
                    result.add_warning(
                        f"Structure Set ROI Sequence item {i}: "
                        f"Referenced Frame of Reference UID '{referenced_frame_uid}' "
                        "is not present in Referenced Frame of Reference Sequence (3006,0010). "
                        "This may indicate inconsistent frame reference definitions."
                    )

        # Validate sequence count consistency
        if ref_frame_seq and not structure_set_roi_seq:
            result.add_warning(
                "Referenced Frame of Reference Sequence is present but Structure Set ROI Sequence is empty. "
                "Frame references are typically used to define ROI spatial context."
            )

        return result

    @staticmethod
    def validate(data: Union[Dataset, 'BaseModule'], config: ValidationConfig | None = None) -> ValidationResult:
        """Orchestrate all validations.

        Args:
            data: pydicom Dataset OR BaseModule instance for zero-copy validation
            config: Optional validation configuration

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        config = config or ValidationConfig()
        result = ValidationResult()

        # Always validate required elements
        result.merge(StructureSetValidator.validate_required_elements(data))

        if config.validate_conditional_requirements:
            result.merge(StructureSetValidator.validate_conditional_requirements(data))

        if config.check_enumerated_values:
            result.merge(StructureSetValidator.validate_enumerated_values(data))

        if config.validate_sequences:
            result.merge(StructureSetValidator.validate_sequence_structures(data))

        # Always validate cross-field dependencies
        result.merge(StructureSetValidator.validate_cross_field_dependencies(data))

        return result
    
    @staticmethod
    def _validate_roi_item(roi_item: Dataset, item_index: int, result: ValidationResult) -> None:
        """Validate individual Structure Set ROI Sequence item."""

        # ROI Number (3006,0022) - Type 1
        if 'ROINumber' not in roi_item or not roi_item.ROINumber:
            result.add_error(
                f"Structure Set ROI Sequence item {item_index}: "
                "ROI Number (3006,0022) is required (Type 1). "
                "This number uniquely identifies the ROI within the Structure Set."
            )

        # Referenced Frame of Reference UID (3006,0024) - Type 1
        if 'ReferencedFrameOfReferenceUID' not in roi_item or not roi_item.ReferencedFrameOfReferenceUID:
            result.add_error(
                f"Structure Set ROI Sequence item {item_index}: "
                "Referenced Frame of Reference UID (3006,0024) is required (Type 1). "
                "This UID must reference a Frame of Reference in the Referenced Frame of Reference Sequence."
            )

        # ROI Name (3006,0026) - Type 2 (required but may be empty)
        if 'ROIName' not in roi_item:
            result.add_error(
                f"Structure Set ROI Sequence item {item_index}: "
                "ROI Name (3006,0026) is required (Type 2). "
                "This user-defined name identifies the ROI. May be empty if name is not specified."
            )

        # ROI Generation Algorithm (3006,0036) - Type 2 (required but may be empty)
        if 'ROIGenerationAlgorithm' not in roi_item:
            result.add_error(
                f"Structure Set ROI Sequence item {item_index}: "
                "ROI Generation Algorithm (3006,0036) is required (Type 2). "
                "This indicates how the ROI was generated (AUTOMATIC, SEMIAUTOMATIC, or MANUAL). "
                "May be empty if algorithm is not specified."
            )
    
    @staticmethod
    def _validate_roi_conditional_elements(roi_item: Dataset, item_index: int, result: ValidationResult) -> None:
        """Validate Type 1C conditional elements in ROI items."""

        # Check for Definition Source Sequence conditional requirements
        if 'DefinitionSourceSequence' in roi_item:
            def_source_seq = roi_item.DefinitionSourceSequence
            for j, def_source_item in enumerate(def_source_seq):
                # Referenced SOP Class UID determines what other elements are required
                if 'ReferencedSOPClassUID' in def_source_item:
                    referenced_sop_class_uid = def_source_item.ReferencedSOPClassUID

                    # Referenced Segment Number (0062,000B) - Type 1C
                    # Required if Referenced SOP Class UID is Segmentation Storage
                    if referenced_sop_class_uid == "1.2.840.10008.5.1.4.1.1.66.4":
                        if 'ReferencedSegmentNumber' not in def_source_item or not def_source_item.ReferencedSegmentNumber:
                            result.add_error(
                                f"Structure Set ROI Sequence item {item_index}, Definition Source Sequence item {j}: "
                                "Referenced Segment Number (0062,000B) is required (Type 1C) "
                                "when Referenced SOP Class UID is Segmentation Storage (1.2.840.10008.5.1.4.1.1.66.4)."
                            )

                    # Referenced Fiducial UID (0070,031B) - Type 1C
                    # Required if Referenced SOP Class UID is Spatial Fiducials Storage
                    elif referenced_sop_class_uid == "1.2.840.10008.5.1.4.1.1.66.2":
                        if 'ReferencedFiducialUID' not in def_source_item or not def_source_item.ReferencedFiducialUID:
                            result.add_error(
                                f"Structure Set ROI Sequence item {item_index}, Definition Source Sequence item {j}: "
                                "Referenced Fiducial UID (0070,031B) is required (Type 1C) "
                                "when Referenced SOP Class UID is Spatial Fiducials Storage (1.2.840.10008.5.1.4.1.1.66.2)."
                            )

