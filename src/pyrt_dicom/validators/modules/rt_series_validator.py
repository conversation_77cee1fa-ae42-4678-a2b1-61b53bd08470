"""RT Series Module DICOM validation - PS3.3 C.8.8.1

This validator ensures 100% compliance with DICOM PS3.3 C.8.8.1 RT Series Module
specifications, including proper validation of RT-specific requirements, conditional
logic, and sequence structures for radiotherapy objects.

Key Validation Areas:
- Type 1 required elements (Modality, Series Instance UID)
- Type 2 required elements (Operators' Name)
- Type 3 optional elements with proper enumerated value checking
- Conditional sequence requirements and cardinality constraints
- RT-specific modality validation and IOD-specific requirements
- Cross-field consistency validation for operator identification
"""

from typing import TYPE_CHECKING, Union
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule


class RTSeriesValidator(BaseValidator):
    """Validator for DICOM RT Series Module (PS3.3 C.8.8.1).

    Provides comprehensive validation of RT Series Module requirements for radiotherapy
    objects, ensuring full compliance with DICOM standard requirements for RT series
    identification and selection within the Query/Retrieve model.

    Validation Coverage:
    - Type 1 required elements with RT-specific modality constraints
    - Type 2 required elements with proper empty value handling
    - Type 3 optional elements with enumerated value validation
    - Conditional sequence requirements and operator identification consistency
    - RT-specific temporal and workflow requirements
    - Cross-field validation for operator and identification sequences

    Error Message Standards:
    - Include DICOM tag references (e.g., "(0008,0060)")
    - Reference DICOM PS3.3 sections for complex requirements
    - Provide actionable guidance for resolving issues
    - Explain the RT-specific context and clinical workflow requirements
    - Suggest valid values for RT-specific enumerated attributes
    """

    @staticmethod
    def validate_required_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1 and Type 2 required elements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        result = ValidationResult()

        # Type 1 required elements
        # Modality (0008,0060) - Type 1
        if 'Modality' not in data or not data.Modality:
            result.add_error(
                "Modality (0008,0060) is required (Type 1). "
                "The type of equipment that originally acquired the data used to create the "
                "Instances in this Series must be specified. For RT Series, valid values are: "
                "RTIMAGE, RTDOSE, RTSTRUCT, RTPLAN, RTRECORD. "
                "See DICOM PS3.3 C.******* for IOD-specific requirements. "
                "Solution: Set Modality to one of the valid RT enumerated values."
            )

        # Series Instance UID (0020,000E) - Type 1
        if 'SeriesInstanceUID' not in data or not data.SeriesInstanceUID:
            result.add_error(
                "Series Instance UID (0020,000E) is required (Type 1). "
                "A unique identifier for the Series must be provided to enable proper "
                "DICOM Query/Retrieve operations and series identification. "
                "Solution: Set SeriesInstanceUID to a valid DICOM UID."
            )

        # Type 2 required elements
        # Operators' Name (0008,1070) - Type 2
        if 'OperatorsName' not in data:
            result.add_error(
                "Operators' Name (0008,1070) is required (Type 2). "
                "The name(s) of the operator(s) supporting the Series must be present, "
                "even if empty. This attribute is essential for RT workflow tracking "
                "and quality assurance. "
                "Solution: Set OperatorsName to operator name(s) or empty string if unknown."
            )

        return result

    @staticmethod
    def validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1C and Type 2C conditional requirements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        result = ValidationResult()

        # Operator Identification Sequence correspondence with Operators' Name
        if 'OperatorIdentificationSequence' in data and 'OperatorsName' in data:
            operator_seq = data.OperatorIdentificationSequence
            operator_name = data.OperatorsName

            # Check that number and order correspond if both are present
            # pydicom automatically converts backslash-separated strings to MultiValue (list-like)
            from pydicom.valuerep import PersonName
            from pydicom.multival import MultiValue

            if isinstance(operator_name, MultiValue):
                # Handle MultiValue format (multiple operators)
                operator_names = list(operator_name)
            elif isinstance(operator_name, (str, PersonName)):
                # Handle single operator (string or PersonName)
                operator_names = [operator_name]
            else:
                # Handle other formats
                operator_names = [operator_name]

            if len(operator_seq) != len(operator_names):
                result.add_warning(
                    "Operator Identification Sequence (0008,1072) correspondence issue: "
                    f"Number of sequence items ({len(operator_seq)}) should correspond to "
                    f"number of Operators' Name values ({len(operator_names)}). "
                    "If more than one Item, the number and order shall correspond to the "
                    "Value of Operators' Name (0008,1070), if present. "
                    "See DICOM PS3.3 C.8.8.1 for operator identification requirements."
                )

        return result

    @staticmethod
    def validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate enumerated value constraints.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        result = ValidationResult()

        # Modality (0008,0060) - RT-specific enumerated values
        if 'Modality' in data:
            modality = data.Modality
            if modality:
                rt_modalities = ["RTIMAGE", "RTDOSE", "RTSTRUCT", "RTPLAN", "RTRECORD"]
                if modality not in rt_modalities:
                    result.add_error(
                        f"Modality (0008,0060) has invalid value '{modality}' for RT Series. "
                        f"Valid RT modality values are: {', '.join(rt_modalities)}. "
                        "The enumerated value must be determined by the IOD: "
                        "RTIMAGE (RT Image IOD), RTDOSE (RT Dose IOD), RTSTRUCT (RT Structure Set IOD), "
                        "RTPLAN (RT Plan/Ion Plan IOD), RTRECORD (RT Treatment Record IODs). "
                        "See DICOM PS3.3 C.******* for IOD-specific requirements."
                    )

        return result

    @staticmethod
    def validate_sequence_structures(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate sequence structure requirements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        result = ValidationResult()

        # Series Description Code Sequence (0008,103F) - Type 3, but if present, only single item allowed
        if 'SeriesDescriptionCodeSequence' in data:
            series_desc_seq = data.SeriesDescriptionCodeSequence
            if len(series_desc_seq) > 1:
                result.add_error(
                    "Series Description Code Sequence (0008,103F) constraint violation: "
                    f"Only a single Item is permitted in this Sequence, found {len(series_desc_seq)} items. "
                    "This sequence provides a coded description of the Series and must contain "
                    "exactly one code sequence item when present. "
                    "Solution: Reduce sequence to contain only one code sequence item."
                )

            # Validate Series Description Code Sequence items if present
            for i, item in enumerate(series_desc_seq):
                RTSeriesValidator._validate_code_sequence_item(item, f"Series Description Code Sequence item {i}", result)

        # Operator Identification Sequence (0008,1072) - Type 3
        if 'OperatorIdentificationSequence' in data:
            operator_seq = data.OperatorIdentificationSequence
            for i, item in enumerate(operator_seq):
                RTSeriesValidator._validate_operator_identification_item(item, i, result)

        # Referenced Performed Procedure Step Sequence (0008,1111) - Type 3
        if 'ReferencedPerformedProcedureStepSequence' in data:
            ref_pps_seq = data.ReferencedPerformedProcedureStepSequence
            for i, item in enumerate(ref_pps_seq):
                RTSeriesValidator._validate_sop_instance_reference_item(
                    item, f"Referenced Performed Procedure Step Sequence item {i}", result
                )

        # Request Attributes Sequence (0040,0275) - Type 3
        if 'RequestAttributesSequence' in data:
            request_seq = data.RequestAttributesSequence
            for i, item in enumerate(request_seq):
                RTSeriesValidator._validate_request_attributes_item(item, i, result)

        return result

    @staticmethod
    def validate(data: Union[Dataset, 'BaseModule'], config: Union[ValidationConfig, None] = None) -> ValidationResult:
        """Orchestrate all validations.

        Args:
            data: pydicom Dataset OR BaseModule instance for zero-copy validation
            config: Optional validation configuration

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        config = config or ValidationConfig()
        result = ValidationResult()

        # Always validate required elements
        result.merge(RTSeriesValidator.validate_required_elements(data))

        if config.validate_conditional_requirements:
            result.merge(RTSeriesValidator.validate_conditional_requirements(data))

        if config.check_enumerated_values:
            result.merge(RTSeriesValidator.validate_enumerated_values(data))

        if config.validate_sequences:
            result.merge(RTSeriesValidator.validate_sequence_structures(data))

        return result


    @staticmethod
    def _validate_code_sequence_item(item: Dataset, context: str, result: ValidationResult) -> None:
        """Validate Code Sequence Macro attributes in sequence items."""
        # Code Value (0008,0100) - Type 1 in Code Sequence Macro
        if 'CodeValue' not in item or not item.CodeValue:
            result.add_error(f"{context}: Code Value (0008,0100) is required in Code Sequence Macro")

        # Coding Scheme Designator (0008,0102) - Type 1 in Code Sequence Macro
        if 'CodingSchemeDesignator' not in item or not item.CodingSchemeDesignator:
            result.add_error(f"{context}: Coding Scheme Designator (0008,0102) is required in Code Sequence Macro")

        # Code Meaning (0008,0104) - Type 1 in Code Sequence Macro
        if 'CodeMeaning' not in item or not item.CodeMeaning:
            result.add_error(f"{context}: Code Meaning (0008,0104) is required in Code Sequence Macro")

    @staticmethod
    def _validate_operator_identification_item(item: Dataset, index: int, result: ValidationResult) -> None:
        """Validate Person Identification Macro attributes in Operator Identification Sequence items."""
        context = f"Operator Identification Sequence item {index}"

        # Person Identification Code Sequence (0040,1101) - Type 2 in Person Identification Macro
        if 'PersonIdentificationCodeSequence' not in item:
            result.add_warning(
                f"{context}: Person Identification Code Sequence (0040,1101) is recommended "
                "(Type 2 in Person Identification Macro)"
            )

    @staticmethod
    def _validate_sop_instance_reference_item(item: Dataset, context: str, result: ValidationResult) -> None:
        """Validate SOP Instance Reference Macro attributes in sequence items."""
        # Referenced SOP Class UID (0008,1150) - Type 1 in SOP Instance Reference Macro
        if 'ReferencedSOPClassUID' not in item or not item.ReferencedSOPClassUID:
            result.add_error(f"{context}: Referenced SOP Class UID (0008,1150) is required in SOP Instance Reference Macro")

        # Referenced SOP Instance UID (0008,1155) - Type 1 in SOP Instance Reference Macro
        if 'ReferencedSOPInstanceUID' not in item or not item.ReferencedSOPInstanceUID:
            result.add_error(f"{context}: Referenced SOP Instance UID (0008,1155) is required in SOP Instance Reference Macro")

    @staticmethod
    def _validate_request_attributes_item(item: Dataset, index: int, result: ValidationResult) -> None:
        """Validate Request Attributes Macro attributes in Request Attributes Sequence items."""
        context = f"Request Attributes Sequence item {index}"

        # Requested Procedure ID (0040,1001) - Type 1 in Request Attributes Macro
        if 'RequestedProcedureID' not in item or not item.RequestedProcedureID:
            result.add_error(f"{context}: Requested Procedure ID (0040,1001) is required in Request Attributes Macro")

        # Scheduled Procedure Step ID (0040,0009) - Type 1 in Request Attributes Macro
        if 'ScheduledProcedureStepID' not in item or not item.ScheduledProcedureStepID:
            result.add_error(f"{context}: Scheduled Procedure Step ID (0040,0009) is required in Request Attributes Macro")
