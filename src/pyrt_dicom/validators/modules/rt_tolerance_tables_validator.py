"""RT Tolerance Tables Module DICOM validation - PS3.3 C.8.8.11

Validates RT Tolerance Tables Module compliance including:
- Required elements (Tolerance Table Number)
- Conditional requirements (nested sequence elements)
- Enumerated values (RT Beam Limiting Device Type)
- Uniqueness constraints (Tolerance Table Numbers)
- Value consistency and clinical warnings
"""

from typing import TYPE_CHECKING, Union
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.rt_enums import RTBeamLimitingDeviceType

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule


class RTToleranceTablesValidator(BaseValidator):
    """Validator for DICOM RT Tolerance Tables Module (PS3.3 C.8.8.11)."""

    @staticmethod
    def validate_required_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1 and Type 2 required elements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        result = ValidationResult()

        # Note: RT Tolerance Tables Module has no Type 1 or Type 2 elements at the top level
        # All elements are Type 3 (optional). However, within sequences there are Type 1 elements.

        if 'ToleranceTableSequence' in data:
            tolerance_sequence = data.ToleranceTableSequence
            for i, table_item in enumerate(tolerance_sequence):
                # Tolerance Table Number is Type 1 (required) for each table in sequence
                if 'ToleranceTableNumber' not in table_item:
                    result.add_error(
                        f"Tolerance Table Sequence item {i}: "
                        f"Tolerance Table Number (300A,0042) is required (Type 1) "
                        f"per DICOM PS3.3 C.8.8.11"
                    )
                elif not isinstance(table_item.ToleranceTableNumber, int) or table_item.ToleranceTableNumber <= 0:
                    result.add_error(
                        f"Tolerance Table Sequence item {i}: "
                        f"Tolerance Table Number (300A,0042) must be a positive integer, "
                        f"got {table_item.ToleranceTableNumber}"
                    )

        return result

    @staticmethod
    def validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1C and Type 2C conditional requirements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        result = ValidationResult()

        if 'ToleranceTableSequence' in data:
            tolerance_sequence = data.ToleranceTableSequence
            for i, table_item in enumerate(tolerance_sequence):
                # Validate Beam Limiting Device Tolerance Sequence conditional requirements
                # When this sequence is present, both nested elements become Type 1 (required)
                if 'BeamLimitingDeviceToleranceSequence' in table_item:
                    bld_tolerance_seq = table_item.BeamLimitingDeviceToleranceSequence
                    for j, bld_item in enumerate(bld_tolerance_seq):
                        # RT Beam Limiting Device Type is Type 1 when sequence is present
                        if 'RTBeamLimitingDeviceType' not in bld_item:
                            result.add_error(
                                f"Beam Limiting Device Tolerance Sequence item {j} in Table {i}: "
                                f"RT Beam Limiting Device Type (300A,00B8) is required (Type 1) "
                                f"when Beam Limiting Device Tolerance Sequence is present"
                            )
                        else:
                            device_type = bld_item.RTBeamLimitingDeviceType
                            if not isinstance(device_type, str) or not device_type.strip():
                                result.add_error(
                                    f"Beam Limiting Device Tolerance Sequence item {j} in Table {i}: "
                                    f"RT Beam Limiting Device Type (300A,00B8) is required (Type 1) "
                                    f"when Beam Limiting Device Tolerance Sequence is present"
                                )

                        # Beam Limiting Device Position Tolerance is Type 1 when sequence is present
                        if 'BeamLimitingDevicePositionTolerance' not in bld_item:
                            result.add_error(
                                f"Beam Limiting Device Tolerance Sequence item {j} in Table {i}: "
                                f"Beam Limiting Device Position Tolerance (300A,004A) is required (Type 1) "
                                f"when Beam Limiting Device Tolerance Sequence is present"
                            )
                        elif not isinstance(bld_item.BeamLimitingDevicePositionTolerance, (int, float)):
                            result.add_error(
                                f"Beam Limiting Device Tolerance Sequence item {j} in Table {i}: "
                                f"Beam Limiting Device Position Tolerance (300A,004A) must be numeric, "
                                f"got {bld_item.BeamLimitingDevicePositionTolerance} ({type(bld_item.BeamLimitingDevicePositionTolerance).__name__})"
                            )

        return result

    @staticmethod
    def validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate enumerated value constraints.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        result = ValidationResult()

        if 'ToleranceTableSequence' in data:
            tolerance_sequence = data.ToleranceTableSequence
            for i, table_item in enumerate(tolerance_sequence):
                # Validate RT Beam Limiting Device Type in Beam Limiting Device Tolerance Sequence
                if 'BeamLimitingDeviceToleranceSequence' in table_item:
                    bld_tolerance_seq = table_item.BeamLimitingDeviceToleranceSequence
                    for j, bld_item in enumerate(bld_tolerance_seq):
                        if 'RTBeamLimitingDeviceType' in bld_item:
                            device_type = bld_item.RTBeamLimitingDeviceType
                            if device_type:
                                valid_device_types = [dtype.value for dtype in RTBeamLimitingDeviceType]
                                BaseValidator.validate_enumerated_value(
                                    device_type, valid_device_types,
                                    f"RT Beam Limiting Device Type (300A,00B8) in BLD Tolerance {j}, Table {i}", result
                                )

        return result

    @staticmethod
    def validate_sequence_structures(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate sequence structure requirements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        result = ValidationResult()

        # Validate uniqueness constraints
        if 'ToleranceTableSequence' in data:
            tolerance_sequence = data.ToleranceTableSequence

            # Validate uniqueness of tolerance table numbers within the RT Plan
            # Per DICOM standard: "The Value of Tolerance Table Number (300A,0042)
            # shall be unique within the RT Plan in which it is created."
            table_numbers = []
            for i, table_item in enumerate(tolerance_sequence):
                if 'ToleranceTableNumber' in table_item:
                    table_number = table_item.ToleranceTableNumber
                    if table_number in table_numbers:
                        result.add_error(
                            f"Tolerance Table Sequence item {i}: "
                            f"Tolerance Table Number ({table_number}) must be unique within the RT Plan "
                            f"(DICOM PS3.3 C.8.8.11). Already exists in sequence."
                        )
                    else:
                        table_numbers.append(table_number)

        return result

    @staticmethod
    def validate(data: Union[Dataset, 'BaseModule'], config: ValidationConfig | None = None) -> ValidationResult:
        """Orchestrate all validations.

        Args:
            data: pydicom Dataset OR BaseModule instance for zero-copy validation
            config: Optional validation configuration

        Returns:
            ValidationResult: Validation result with errors and warnings
        """
        config = config or ValidationConfig()
        result = ValidationResult()

        # Always validate required elements
        result.merge(RTToleranceTablesValidator.validate_required_elements(data))

        if config.validate_conditional_requirements:
            result.merge(RTToleranceTablesValidator.validate_conditional_requirements(data))

        if config.check_enumerated_values:
            result.merge(RTToleranceTablesValidator.validate_enumerated_values(data))

        if config.validate_sequences:
            result.merge(RTToleranceTablesValidator.validate_sequence_structures(data))

        # Validate tolerance value consistency (always run for clinical safety)
        result.merge(RTToleranceTablesValidator._validate_tolerance_value_consistency(data))

        return result
    
    @staticmethod
    def _validate_tolerance_value_consistency(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate tolerance value consistency and logical constraints.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with warnings for inconsistent values
        """
        result = ValidationResult()

        if 'ToleranceTableSequence' in data:
            tolerance_sequence = data.ToleranceTableSequence
            for i, table_item in enumerate(tolerance_sequence):
                # Validate angular tolerance values are non-negative and within reasonable bounds
                angular_tolerances = [
                    ('GantryAngleTolerance', '300A,0044'),
                    ('GantryPitchAngleTolerance', '300A,014E'),
                    ('BeamLimitingDeviceAngleTolerance', '300A,0046'),
                    ('PatientSupportAngleTolerance', '300A,004C'),
                    ('TableTopEccentricAngleTolerance', '300A,004E'),
                    ('TableTopPitchAngleTolerance', '300A,004F'),
                    ('TableTopRollAngleTolerance', '300A,0050')
                ]

                for field_name, tag in angular_tolerances:
                    if field_name in table_item:
                        tolerance_value = getattr(table_item, field_name)
                        if tolerance_value < 0:
                            result.add_warning(
                                f"Tolerance Table Sequence item {i}: "
                                f"{field_name} ({tag}) should be non-negative, got {tolerance_value}"
                            )
                        elif tolerance_value > 360:
                            result.add_warning(
                                f"Tolerance Table Sequence item {i}: "
                                f"{field_name} ({tag}) exceeds 360 degrees, got {tolerance_value}"
                            )
                        elif tolerance_value < 0.01:
                            result.add_warning(
                                f"Tolerance Table Sequence item {i}: "
                                f"{field_name} ({tag}) value ({tolerance_value} degrees) seems unusually small"
                            )

                # Validate positional tolerance values are non-negative
                positional_tolerances = [
                    ('TableTopVerticalPositionTolerance', '300A,0051'),
                    ('TableTopLongitudinalPositionTolerance', '300A,0052'),
                    ('TableTopLateralPositionTolerance', '300A,0053')
                ]

                for field_name, tag in positional_tolerances:
                    if field_name in table_item:
                        tolerance_value = getattr(table_item, field_name)
                        if tolerance_value < 0:
                            result.add_warning(
                                f"Tolerance Table Sequence item {i}: "
                                f"{field_name} ({tag}) should be non-negative, got {tolerance_value}"
                            )
                        elif tolerance_value < 0.01:
                            result.add_warning(
                                f"Tolerance Table Sequence item {i}: "
                                f"{field_name} ({tag}) value ({tolerance_value} mm) seems unusually small"
                            )

                # Validate beam limiting device position tolerances
                if 'BeamLimitingDeviceToleranceSequence' in table_item:
                    bld_tolerance_seq = table_item.BeamLimitingDeviceToleranceSequence
                    for j, bld_item in enumerate(bld_tolerance_seq):
                        if 'BeamLimitingDevicePositionTolerance' in bld_item:
                            position_tolerance = bld_item.BeamLimitingDevicePositionTolerance
                            if position_tolerance < 0:
                                result.add_warning(
                                    f"Beam Limiting Device Tolerance Sequence item {j} in Table {i}: "
                                    f"Beam Limiting Device Position Tolerance (300A,004A) should be non-negative, got {position_tolerance}"
                                )
                            elif position_tolerance < 0.01:
                                result.add_warning(
                                    f"Beam Limiting Device Tolerance Sequence item {j} in Table {i}: "
                                    f"Beam Limiting Device Position Tolerance (300A,004A) value ({position_tolerance} mm) seems unusually small"
                                )

                # Check for reasonable tolerance values - clinical warnings
                if 'GantryAngleTolerance' in table_item:
                    gantry_tolerance = table_item.GantryAngleTolerance
                    if gantry_tolerance > 10:
                        result.add_warning(
                            f"Tolerance Table Sequence item {i}: "
                            f"Gantry Angle Tolerance ({gantry_tolerance} degrees) seems unusually large for clinical use"
                        )

        return result

