"""SOP Common Module DICOM validation - PS3.3 C.12.1

This validator ensures full compliance with DICOM PS3.3 Section C.12.1
SOP Common Module specifications, including all conditional requirements
and complex sequence validations.
"""

from typing import Union, TYPE_CHECKING
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.common_enums import (
    SyntheticData, SOPInstanceStatus, QueryRetrieveView,
    ContentQualification, LongitudinalTemporalInformationModified
)

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule


class SOPCommonValidator(BaseValidator):
    """Validator for DICOM SOP Common Module (PS3.3 C.12.1)."""

    @staticmethod
    def validate_required_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1 and Type 2 required elements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        result = ValidationResult()

        # Type 1: SOP Class UID (0008,0016)
        if 'SOPClassUID' not in data:
            result.add_error("SOP Class UID (0008,0016) is required (Type 1)")
        elif not data.SOPClassUID:
            result.add_error("SOP Class UID (0008,0016) cannot be empty (Type 1)")

        # Type 1: SOP Instance UID (0008,0018)
        if 'SOPInstanceUID' not in data:
            result.add_error("SOP Instance UID (0008,0018) is required (Type 1)")
        elif not data.SOPInstanceUID:
            result.add_error("SOP Instance UID (0008,0018) cannot be empty (Type 1)")

        return result

    @staticmethod
    def validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1C and Type 2C conditional requirements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        result = ValidationResult()

        # Type 1C: Specific Character Set (0008,0005) - required if expanded or replacement character set is used
        SOPCommonValidator._validate_specific_character_set_conditional(data, result)

        # Type 1C: Query/Retrieve View (0008,0053) - required if instance converted from C-MOVE operation
        SOPCommonValidator._validate_query_retrieve_view_conditional(data, result)

        # Type 1C: Conversion Source Attributes Sequence (0020,9172) - required if created by conversion
        SOPCommonValidator._validate_conversion_source_conditional(data, result)

        # Type 1C: HL7 Structured Document Reference Sequence (0040,A390) - required if HL7 docs referenced
        SOPCommonValidator._validate_hl7_document_reference_conditional(data, result)

        # Type 1C: Encrypted Attributes Sequence (0400,0500) - required if confidentiality needed
        SOPCommonValidator._validate_encrypted_attributes_conditional(data, result)

        # Coding Scheme Identification Sequence conditional requirements
        SOPCommonValidator._validate_coding_scheme_conditionals(data, result)

        # Private Data Element Characteristics conditional requirements
        SOPCommonValidator._validate_private_data_element_conditionals(data, result)

        return result

    @staticmethod
    def validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate enumerated value constraints.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        result = ValidationResult()

        # Synthetic Data (0008,001C)
        if 'SyntheticData' in data:
            synthetic_data = data.SyntheticData
            if synthetic_data:
                valid_values = [val.value for val in SyntheticData]
                BaseValidator.validate_enumerated_value(
                    synthetic_data, valid_values,
                    "Synthetic Data (0008,001C)", result
                )

        # SOP Instance Status (0100,0410)
        if 'SOPInstanceStatus' in data:
            sop_status = data.SOPInstanceStatus
            if sop_status:
                valid_values = [val.value for val in SOPInstanceStatus]
                BaseValidator.validate_enumerated_value(
                    sop_status, valid_values,
                    "SOP Instance Status (0100,0410)", result
                )

        # Query/Retrieve View (0008,0053)
        if 'QueryRetrieveView' in data:
            query_view = data.QueryRetrieveView
            if query_view:
                valid_values = [val.value for val in QueryRetrieveView]
                BaseValidator.validate_enumerated_value(
                    query_view, valid_values,
                    "Query/Retrieve View (0008,0053)", result
                )

        # Content Qualification (0018,9004)
        if 'ContentQualification' in data:
            content_qual = data.ContentQualification
            if content_qual:
                valid_values = [val.value for val in ContentQualification]
                BaseValidator.validate_enumerated_value(
                    content_qual, valid_values,
                    "Content Qualification (0018,9004)", result
                )

        # Longitudinal Temporal Information Modified (0028,0303)
        if 'LongitudinalTemporalInformationModified' in data:
            temporal_modified = data.LongitudinalTemporalInformationModified
            if temporal_modified:
                valid_values = [val.value for val in LongitudinalTemporalInformationModified]
                BaseValidator.validate_enumerated_value(
                    temporal_modified, valid_values,
                    "Longitudinal Temporal Information Modified (0028,0303)", result
                )

        return result

    @staticmethod
    def validate_sequence_structures(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate sequence structure requirements.

        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        result = ValidationResult()

        # Coding Scheme Identification Sequence - validate required elements
        SOPCommonValidator._validate_coding_scheme_identification_sequence(data, result)

        # Context Group Identification Sequence - validate required elements
        SOPCommonValidator._validate_context_group_identification_sequence(data, result)

        # Mapping Resource Identification Sequence - validate required elements
        SOPCommonValidator._validate_mapping_resource_identification_sequence(data, result)

        # Contributing Equipment Sequence - validate required elements
        SOPCommonValidator._validate_contributing_equipment_sequence(data, result)

        # Private Data Element Characteristics Sequence - validate structure
        SOPCommonValidator._validate_private_data_element_characteristics_sequence(data, result)

        # HL7 Structured Document Reference Sequence - validate structure
        SOPCommonValidator._validate_hl7_structured_document_reference_sequence(data, result)

        # Conversion Source Attributes Sequence - validate structure
        SOPCommonValidator._validate_conversion_source_attributes_sequence(data, result)

        return result

    @staticmethod
    def validate(data: Union[Dataset, 'BaseModule'], config: ValidationConfig | None = None) -> ValidationResult:
        """Orchestrate all validations.

        Args:
            data: pydicom Dataset OR BaseModule instance for zero-copy validation
            config: Optional validation configuration

        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        config = config or ValidationConfig()
        result = ValidationResult()

        # Always validate required elements
        result.merge(SOPCommonValidator.validate_required_elements(data))

        if config.validate_conditional_requirements:
            result.merge(SOPCommonValidator.validate_conditional_requirements(data))

        if config.check_enumerated_values:
            result.merge(SOPCommonValidator.validate_enumerated_values(data))

        if config.validate_sequences:
            result.merge(SOPCommonValidator.validate_sequence_structures(data))

        return result
    
    @staticmethod
    def _validate_specific_character_set_conditional(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate Specific Character Set (0008,0005) Type 1C conditional requirement."""
        # For now, just check if non-default character sets are used without explicit declaration
        specific_charset = data.SpecificCharacterSet if 'SpecificCharacterSet' in data else None

        # Scan string fields for non-ASCII characters that might require explicit character set
        string_fields = [
            'PatientName', 'InstitutionName', 'StudyDescription', 'SeriesDescription',
            'ManufacturerModelName', 'StationName', 'OperatorName'
        ]

        has_non_ascii = False
        for field in string_fields:
            if field in data:
                # Use getattr for both Dataset and BaseModule compatibility
                value = str(getattr(data, field, ''))
                if any(ord(char) > 127 for char in value):
                    has_non_ascii = True
                    break

        if has_non_ascii and not specific_charset:
            result.add_warning(
                "Specific Character Set (0008,0005) may be required when using non-ASCII characters. "
                "Consider adding explicit character set declaration per DICOM PS3.3 C.********"
            )

    @staticmethod
    def _validate_query_retrieve_view_conditional(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate Query/Retrieve View (0008,0053) Type 1C conditional requirement."""
        # Check for indicators that this instance was converted from a C-MOVE operation
        query_view = data.QueryRetrieveView if 'QueryRetrieveView' in data else None
        conversion_source = data.ConversionSourceAttributesSequence if 'ConversionSourceAttributesSequence' in data else None

        # If conversion source is present but query/retrieve view is missing, this might indicate
        # that the instance was converted via C-MOVE but the view wasn't specified
        if conversion_source and not query_view:
            result.add_warning(
                "Query/Retrieve View (0008,0053) may be required if this Instance was converted "
                "from its source form as result of C-MOVE operation per DICOM PS3.3 C.12.1"
            )

    @staticmethod
    def _validate_conversion_source_conditional(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate Conversion Source Attributes Sequence (0020,9172) Type 1C conditional requirement."""
        conversion_source = data.ConversionSourceAttributesSequence if 'ConversionSourceAttributesSequence' in data else None

        # Check if there are indicators of conversion but no source attribution
        # This is complex to validate without external context, so we provide guidance
        if 'QueryRetrieveView' in data and not conversion_source:
            result.add_warning(
                "Conversion Source Attributes Sequence (0020,9172) may be required if this Instance "
                "was created by conversion from a DICOM source per DICOM PS3.3 C.12.1"
            )

    @staticmethod
    def _validate_hl7_document_reference_conditional(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate HL7 Structured Document Reference Sequence (0040,A390) Type 1C conditional requirement."""
        hl7_ref_seq = data.HL7StructuredDocumentReferenceSequence if 'HL7StructuredDocumentReferenceSequence' in data else None

        # This is difficult to validate without knowing if HL7 documents are referenced elsewhere
        # We can only provide guidance based on the presence of the sequence
        if hl7_ref_seq is None:
            # Check for potential HL7-related references in other fields
            # This is a simplified check - real validation would need more context
            pass  # Complex validation would require domain knowledge

    @staticmethod
    def _validate_encrypted_attributes_conditional(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate Encrypted Attributes Sequence (0400,0500) Type 1C conditional requirement."""
        encrypted_attrs = data.EncryptedAttributesSequence if 'EncryptedAttributesSequence' in data else None

        if encrypted_attrs:
            # Validate the structure of encrypted attributes
            for i, item in enumerate(encrypted_attrs):
                if not item.get('EncryptedContentTransferSyntaxUID'):
                    result.add_error(
                        f"Encrypted Attributes Sequence item {i}: "
                        f"Encrypted Content Transfer Syntax UID (0400,0510) is required"
                    )
                if not item.get('EncryptedContent'):
                    result.add_error(
                        f"Encrypted Attributes Sequence item {i}: "
                        f"Encrypted Content (0400,0520) is required"
                    )

    @staticmethod
    def _validate_coding_scheme_conditionals(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate coding scheme identification sequence conditional requirements."""
        coding_scheme_seq = data.CodingSchemeIdentificationSequence if 'CodingSchemeIdentificationSequence' in data else []

        for i, item in enumerate(coding_scheme_seq):
            # Type 1C: Coding Scheme Registry required if Coding Scheme is registered
            coding_scheme_uid = item.get('CodingSchemeUID')
            coding_scheme_registry = item.get('CodingSchemeRegistry')
            coding_scheme_external_id = item.get('CodingSchemeExternalID')

            # Complex conditional logic for coding schemes
            if coding_scheme_uid and not coding_scheme_registry:
                result.add_warning(
                    f"Coding Scheme Identification Sequence item {i}: "
                    f"Coding Scheme Registry (0008,0112) may be required if Coding Scheme is registered "
                    f"per DICOM PS3.3 C.12.1"
                )

            # Type 2C: Coding Scheme External ID required if registered and UID not present
            if coding_scheme_registry and not coding_scheme_uid and not coding_scheme_external_id:
                result.add_error(
                    f"Coding Scheme Identification Sequence item {i}: "
                    f"Coding Scheme External ID (0008,0114) is required if Coding Scheme is registered "
                    f"and Coding Scheme UID (0008,010C) is not present per DICOM PS3.3 C.12.1"
                )

    @staticmethod
    def _validate_private_data_element_conditionals(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate private data element characteristics sequence conditional requirements."""
        private_seq = data.PrivateDataElementCharacteristicsSequence if 'PrivateDataElementCharacteristicsSequence' in data else []

        for i, item in enumerate(private_seq):
            # Validate the complex conditional logic for private data elements
            block_status = item.get('BlockIdentifyingInformationStatus')
            nonidentifying_elements = item.get('NonidentifyingPrivateElements')

            # Type 1C: NonidentifyingPrivateElements required if BlockIdentifyingInformationStatus is MIXED
            if block_status == 'MIXED' and not nonidentifying_elements:
                result.add_error(
                    f"Private Data Element Characteristics Sequence item {i}: "
                    f"Nonidentifying Private Elements (0008,0304) is required when "
                    f"Block Identifying Information Status is MIXED per DICOM PS3.3 C.12.1.1.7"
                )

            # Validate private data element definition sequence items
            definition_seq = item.get('PrivateDataElementDefinitionSequence', [])
            for j, def_item in enumerate(definition_seq):
                vr = def_item.get('PrivateDataElementValueRepresentation')
                num_items = def_item.get('PrivateDataElementNumberOfItems')

                # Type 1C: Number of Items required if VR is SQ
                if vr == 'SQ' and not num_items:
                    result.add_error(
                        f"Private Data Element Characteristics Sequence item {i}, "
                        f"Definition Sequence item {j}: "
                        f"Private Data Element Number of Items (0008,030B) is required "
                        f"when Value Representation is SQ per DICOM PS3.3 C.12.1.1.7.2"
                    )
    
    @staticmethod
    def _validate_coding_scheme_identification_sequence(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate Coding Scheme Identification Sequence structure."""
        coding_scheme_seq = data.CodingSchemeIdentificationSequence if 'CodingSchemeIdentificationSequence' in data else []

        for i, item in enumerate(coding_scheme_seq):
            # Type 1: Coding Scheme Designator
            if not item.get('CodingSchemeDesignator'):
                result.add_error(
                    f"Coding Scheme Identification Sequence item {i}: "
                    f"Coding Scheme Designator (0008,0102) is required (Type 1)"
                )

    @staticmethod
    def _validate_context_group_identification_sequence(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate Context Group Identification Sequence structure."""
        context_group_seq = data.ContextGroupIdentificationSequence if 'ContextGroupIdentificationSequence' in data else []

        for i, item in enumerate(context_group_seq):
            # Type 1: Context Identifier
            if not item.get('ContextIdentifier'):
                result.add_error(
                    f"Context Group Identification Sequence item {i}: "
                    f"Context Identifier (0008,010F) is required (Type 1)"
                )

            # Type 1: Mapping Resource
            if not item.get('MappingResource'):
                result.add_error(
                    f"Context Group Identification Sequence item {i}: "
                    f"Mapping Resource (0008,0105) is required (Type 1)"
                )

            # Type 1: Context Group Version
            if not item.get('ContextGroupVersion'):
                result.add_error(
                    f"Context Group Identification Sequence item {i}: "
                    f"Context Group Version (0008,0106) is required (Type 1)"
                )

    @staticmethod
    def _validate_mapping_resource_identification_sequence(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate Mapping Resource Identification Sequence structure."""
        mapping_resource_seq = data.MappingResourceIdentificationSequence if 'MappingResourceIdentificationSequence' in data else []

        for i, item in enumerate(mapping_resource_seq):
            # Type 1: Mapping Resource
            if not item.get('MappingResource'):
                result.add_error(
                    f"Mapping Resource Identification Sequence item {i}: "
                    f"Mapping Resource (0008,0105) is required (Type 1)"
                )

    @staticmethod
    def _validate_contributing_equipment_sequence(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate Contributing Equipment Sequence structure."""
        contrib_equip_seq = data.ContributingEquipmentSequence if 'ContributingEquipmentSequence' in data else []

        for i, item in enumerate(contrib_equip_seq):
            # Type 1: Purpose of Reference Code Sequence
            if not item.get('PurposeOfReferenceCodeSequence'):
                result.add_error(
                    f"Contributing Equipment Sequence item {i}: "
                    f"Purpose of Reference Code Sequence (0040,A170) is required (Type 1)"
                )

            # Type 1: Manufacturer
            if not item.get('Manufacturer'):
                result.add_error(
                    f"Contributing Equipment Sequence item {i}: "
                    f"Manufacturer (0008,0070) is required (Type 1)"
                )

    @staticmethod
    def _validate_private_data_element_characteristics_sequence(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate Private Data Element Characteristics Sequence structure."""
        private_seq = data.PrivateDataElementCharacteristicsSequence if 'PrivateDataElementCharacteristicsSequence' in data else []

        for i, item in enumerate(private_seq):
            # Type 1: Private Group Reference
            if not item.get('PrivateGroupReference'):
                result.add_error(
                    f"Private Data Element Characteristics Sequence item {i}: "
                    f"Private Group Reference (0008,0301) is required (Type 1)"
                )

            # Type 1: Private Creator Reference
            if not item.get('PrivateCreatorReference'):
                result.add_error(
                    f"Private Data Element Characteristics Sequence item {i}: "
                    f"Private Creator Reference (0008,0302) is required (Type 1)"
                )

    @staticmethod
    def _validate_hl7_structured_document_reference_sequence(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate HL7 Structured Document Reference Sequence structure."""
        hl7_seq = data.HL7StructuredDocumentReferenceSequence if 'HL7StructuredDocumentReferenceSequence' in data else []

        for i, item in enumerate(hl7_seq):
            # Type 1: HL7 Instance Identifier
            if not item.get('HL7InstanceIdentifier'):
                result.add_error(
                    f"HL7 Structured Document Reference Sequence item {i}: "
                    f"HL7 Instance Identifier (0040,E001) is required (Type 1)"
                )

    @staticmethod
    def _validate_conversion_source_attributes_sequence(data: Union[Dataset, 'BaseModule'], result: ValidationResult) -> None:
        """Validate Conversion Source Attributes Sequence structure."""
        conversion_seq = data.ConversionSourceAttributesSequence if 'ConversionSourceAttributesSequence' in data else []

        for i, item in enumerate(conversion_seq):
            # Should include SOP Instance Reference Macro - check for basic required elements
            if not item.get('ReferencedSOPClassUID'):
                result.add_error(
                    f"Conversion Source Attributes Sequence item {i}: "
                    f"Referenced SOP Class UID is required for SOP Instance Reference"
                )

            if not item.get('ReferencedSOPInstanceUID'):
                result.add_error(
                    f"Conversion Source Attributes Sequence item {i}: "
                    f"Referenced SOP Instance UID is required for SOP Instance Reference"
                )


