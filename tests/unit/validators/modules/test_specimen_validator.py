"""
Test SpecimenValidator functionality.

Tests DICOM PS3.3 C.7.6.22 Specimen Module validation logic including
conditional requirements, sequence cardinality, and enumerated values.
"""

from pydicom import Dataset

from pyrt_dicom.validators.modules.specimen_validator import SpecimenValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators.validation_result import ValidationResult
from pyrt_dicom.modules import SpecimenModule
from pyrt_dicom.enums import ContainerComponentMaterial


class TestSpecimenValidator:
    """Test SpecimenValidator functionality."""
    
    def test_valid_minimal_specimen_passes_validation(self):
        """Test that valid minimal specimen passes validation without errors."""
        # Create valid minimal specimen using module
        specimen_desc = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10"
        )
        
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc]
        )
        
        # Get dataset and validate
        dataset = specimen.to_dataset()
        result = SpecimenValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert isinstance(result.warnings, list)
    
    def test_valid_comprehensive_specimen_passes_validation(self):
        """Test that comprehensive specimen with all elements passes validation."""
        specimen_desc = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10",
            specimen_short_description="Test specimen",
            specimen_detailed_description="Comprehensive test specimen"
        )
        
        issuer_seq = [SpecimenModule.create_hierarchic_designator_item(
            local_namespace_entity_id="LAB_001"
        )]
        
        container_type_seq = [SpecimenModule.create_code_sequence_item(
            code_value="433466003",
            coding_scheme_designator="SCT",
            code_meaning="Microscope slide"
        )]
        
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc]
        ).with_issuer_information(
            issuer_of_container_identifier_sequence=issuer_seq
        ).with_container_type(
            container_type_code_sequence=container_type_seq
        )
        
        dataset = specimen.to_dataset()
        result = SpecimenValidator.validate(dataset)
        
        assert len(result.errors) == 0
        assert isinstance(result.warnings, list)
    
    def test_missing_container_identifier_generates_error(self):
        """Test validation failure for missing Container Identifier (Type 1)."""
        dataset = Dataset()
        dataset.SpecimenDescriptionSequence = [Dataset()]
        dataset.IssuerOfContainerIdentifierSequence = []
        dataset.ContainerTypeCodeSequence = []
        
        result = SpecimenValidator.validate(dataset)
        
        assert len(result.errors) > 0
        error_messages = [error for error in result.errors if "Container Identifier (0040,0512) is missing" in error]
        assert len(error_messages) == 1
        assert "Type 1 element is required" in error_messages[0]
    
    def test_empty_container_identifier_generates_error(self):
        """Test validation failure for empty Container Identifier (Type 1)."""
        dataset = Dataset()
        dataset.ContainerIdentifier = ""  # Empty but present
        dataset.SpecimenDescriptionSequence = [Dataset()]
        dataset.IssuerOfContainerIdentifierSequence = []
        dataset.ContainerTypeCodeSequence = []
        
        result = SpecimenValidator.validate(dataset)
        
        error_messages = [error for error in result.errors if "Container Identifier (0040,0512) cannot be empty" in error]
        assert len(error_messages) == 1
        assert "Type 1 elements must have a value" in error_messages[0]
    
    def test_missing_specimen_description_sequence_generates_error(self):
        """Test validation failure for missing Specimen Description Sequence (Type 1)."""
        dataset = Dataset()
        dataset.ContainerIdentifier = "SLIDE_001"
        dataset.IssuerOfContainerIdentifierSequence = []
        dataset.ContainerTypeCodeSequence = []
        
        result = SpecimenValidator.validate(dataset)
        
        error_messages = [error for error in result.errors if "Specimen Description Sequence (0040,0560) is missing" in error]
        assert len(error_messages) == 1
        assert "Type 1 element is required" in error_messages[0]
    
    def test_empty_specimen_description_sequence_generates_error(self):
        """Test validation failure for empty Specimen Description Sequence (Type 1)."""
        dataset = Dataset()
        dataset.ContainerIdentifier = "SLIDE_001"
        dataset.SpecimenDescriptionSequence = []  # Empty but present
        dataset.IssuerOfContainerIdentifierSequence = []
        dataset.ContainerTypeCodeSequence = []
        
        result = SpecimenValidator.validate(dataset)
        
        error_messages = [error for error in result.errors if "Specimen Description Sequence (0040,0560) cannot be empty" in error]
        assert len(error_messages) == 1
        assert "One or more Items shall be included" in error_messages[0]
    
    def test_missing_type2_elements_generates_errors(self):
        """Test validation failure for missing Type 2 elements."""
        dataset = Dataset()
        dataset.ContainerIdentifier = "SLIDE_001"
        
        # Create minimal specimen description
        spec_item = Dataset()
        spec_item.SpecimenIdentifier = "SPEC_001"
        spec_item.SpecimenUID = "*******.*******.9.10"
        dataset.SpecimenDescriptionSequence = [spec_item]
        
        # Don't set Type 2 elements
        
        result = SpecimenValidator.validate(dataset)
        
        # Should have errors for missing Type 2 elements
        issuer_errors = [error for error in result.errors if "Issuer of Container Identifier Sequence (0040,0513) is missing" in error]
        container_type_errors = [error for error in result.errors if "Container Type Code Sequence (0040,0518) is missing" in error]
        
        assert len(issuer_errors) == 1
        assert len(container_type_errors) == 1
        assert "Type 2 element is required" in issuer_errors[0]
        assert "Type 2 element is required" in container_type_errors[0]
    
    def test_specimen_description_item_validation(self):
        """Test validation of specimen description sequence items."""
        dataset = Dataset()
        dataset.ContainerIdentifier = "SLIDE_001"
        dataset.IssuerOfContainerIdentifierSequence = []
        dataset.ContainerTypeCodeSequence = []
        
        # Create specimen item missing required elements
        spec_item = Dataset()
        # Missing SpecimenIdentifier and SpecimenUID
        dataset.SpecimenDescriptionSequence = [spec_item]
        
        result = SpecimenValidator.validate(dataset)
        
        spec_id_errors = [error for error in result.errors if "Specimen Identifier (0040,0551) is required" in error]
        spec_uid_errors = [error for error in result.errors if "Specimen UID (0040,0554) is required" in error]
        
        assert len(spec_id_errors) == 1
        assert len(spec_uid_errors) == 1
        assert "Specimen Description Sequence item 1" in spec_id_errors[0]
        assert "departmental information system identifier" in spec_id_errors[0]
    
    def test_specimen_localization_conditional_requirement(self):
        """Test Type 1C conditional requirement for specimen localization."""
        dataset = Dataset()
        dataset.ContainerIdentifier = "SLIDE_001"
        dataset.IssuerOfContainerIdentifierSequence = []
        dataset.ContainerTypeCodeSequence = []
        
        # Create multiple specimens (triggers Type 1C requirement)
        spec_item1 = Dataset()
        spec_item1.SpecimenIdentifier = "SPEC_001"
        spec_item1.SpecimenUID = "*******.*******.9.10"
        # Missing SpecimenLocalizationContentItemSequence
        
        spec_item2 = Dataset()
        spec_item2.SpecimenIdentifier = "SPEC_002" 
        spec_item2.SpecimenUID = "*******.*******.9.11"
        # Missing SpecimenLocalizationContentItemSequence
        
        dataset.SpecimenDescriptionSequence = [spec_item1, spec_item2]
        
        result = SpecimenValidator.validate(dataset)
        
        localization_errors = [error for error in result.errors if "Specimen Localization Content Item Sequence (0040,0620)" in error and "Type 1C" in error]
        assert len(localization_errors) == 2  # One error per specimen
        assert "multiple specimens are present" in localization_errors[0]
        assert "location of each specimen" in localization_errors[0]
    
    def test_specimen_localization_not_required_for_single_specimen(self):
        """Test that specimen localization is not required for single specimen."""
        specimen_desc = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10"
        )
        
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc]
        )
        
        dataset = specimen.to_dataset()
        result = SpecimenValidator.validate(dataset)
        
        # Should not have errors about specimen localization for single specimen
        localization_errors = [error for error in result.errors if "Specimen Localization Content Item Sequence" in error]
        assert len(localization_errors) == 0
    
    def test_sequence_cardinality_validation(self):
        """Test sequence cardinality validation."""
        dataset = Dataset()
        dataset.ContainerIdentifier = "SLIDE_001"
        
        # Create valid specimen description
        spec_item = Dataset()
        spec_item.SpecimenIdentifier = "SPEC_001"
        spec_item.SpecimenUID = "*******.*******.9.10"
        dataset.SpecimenDescriptionSequence = [spec_item]
        
        # Create multiple issuer items (violates 0-1 cardinality)
        issuer1 = Dataset()
        issuer1.LocalNamespaceEntityID = "LAB_001"
        issuer2 = Dataset()
        issuer2.LocalNamespaceEntityID = "LAB_002"
        dataset.IssuerOfContainerIdentifierSequence = [issuer1, issuer2]
        
        # Create multiple container type items (violates 0-1 cardinality)
        type1 = Dataset()
        type1.CodeValue = "433466003"
        type1.CodingSchemeDesignator = "SCT"
        type1.CodeMeaning = "Microscope slide"
        type2 = Dataset()
        type2.CodeValue = "123456789"
        type2.CodingSchemeDesignator = "TEST"
        type2.CodeMeaning = "Test container"
        dataset.ContainerTypeCodeSequence = [type1, type2]
        
        result = SpecimenValidator.validate(dataset)
        
        issuer_cardinality_errors = [error for error in result.errors if "Issuer of Container Identifier Sequence (0040,0513) cardinality violation" in error]
        container_cardinality_errors = [error for error in result.errors if "Container Type Code Sequence (0040,0518) cardinality violation" in error]
        
        assert len(issuer_cardinality_errors) == 1
        assert len(container_cardinality_errors) == 1
        assert "Zero or one Item shall be included" in issuer_cardinality_errors[0]
        assert "Found 2 items" in issuer_cardinality_errors[0]
    
    def test_container_component_material_enumeration(self):
        """Test container component material enumerated value validation."""
        dataset = Dataset()
        dataset.ContainerIdentifier = "SLIDE_001"
        dataset.IssuerOfContainerIdentifierSequence = []
        dataset.ContainerTypeCodeSequence = []
        
        # Create valid specimen description
        spec_item = Dataset()
        spec_item.SpecimenIdentifier = "SPEC_001"
        spec_item.SpecimenUID = "*******.*******.9.10"
        dataset.SpecimenDescriptionSequence = [spec_item]
        
        # Create container component with invalid material
        component = Dataset()
        component.ContainerComponentTypeCodeSequence = [Dataset()]  # Minimal code sequence
        component.ContainerComponentMaterial = "INVALID_MATERIAL"  # Not in ContainerComponentMaterial enum
        dataset.ContainerComponentSequence = [component]
        
        config = ValidationConfig(check_enumerated_values=True)
        result = SpecimenValidator.validate(dataset, config)
        
        material_warnings = [warning for warning in result.warnings if "Container Component Material (0050,001A)" in warning and "INVALID_MATERIAL" in warning]
        assert len(material_warnings) == 1
        assert "should be one of" in material_warnings[0]
        # Should suggest valid enum values: GLASS, PLASTIC, METAL
        valid_values = [str(material.value) for material in ContainerComponentMaterial]
        assert any(value in material_warnings[0] for value in valid_values)
    
    def test_code_sequence_structure_validation(self):
        """Test code sequence structure validation."""
        dataset = Dataset()
        dataset.ContainerIdentifier = "SLIDE_001"
        dataset.IssuerOfContainerIdentifierSequence = []
        
        # Create valid specimen description
        spec_item = Dataset()
        spec_item.SpecimenIdentifier = "SPEC_001"
        spec_item.SpecimenUID = "*******.*******.9.10"
        dataset.SpecimenDescriptionSequence = [spec_item]
        
        # Create invalid code sequence (missing required elements)
        invalid_code = Dataset()
        # Missing CodeValue, CodingSchemeDesignator, CodeMeaning
        dataset.ContainerTypeCodeSequence = [invalid_code]
        
        config = ValidationConfig(validate_sequences=True)
        result = SpecimenValidator.validate(dataset, config)
        
        code_value_errors = [error for error in result.errors if "Code Value (0008,0100) is required" in error]
        coding_scheme_errors = [error for error in result.errors if "Coding Scheme Designator (0008,0102) is required" in error]
        code_meaning_errors = [error for error in result.errors if "Code Meaning (0008,0104) is required" in error]
        
        assert len(code_value_errors) == 1
        assert len(coding_scheme_errors) == 1
        assert len(code_meaning_errors) == 1
        assert "Container Type Code Sequence item 1" in code_value_errors[0]
    
    def test_hierarchic_designator_conditional_requirements(self):
        """Test hierarchic designator conditional validation."""
        dataset = Dataset()
        dataset.ContainerIdentifier = "SLIDE_001"
        dataset.ContainerTypeCodeSequence = []
        
        # Create valid specimen description
        spec_item = Dataset()
        spec_item.SpecimenIdentifier = "SPEC_001"
        spec_item.SpecimenUID = "*******.*******.9.10"
        dataset.SpecimenDescriptionSequence = [spec_item]
        
        # Create invalid hierarchic designator (missing both required fields)
        issuer = Dataset()
        # Missing both LocalNamespaceEntityID and UniversalEntityID
        dataset.IssuerOfContainerIdentifierSequence = [issuer]
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = SpecimenValidator.validate(dataset, config)
        
        hierarchic_errors = [error for error in result.errors if "Either Local Namespace Entity ID" in error and "Universal Entity ID" in error]
        assert len(hierarchic_errors) == 1
        assert "Type 1C" in hierarchic_errors[0]
    
    def test_universal_entity_id_type_conditional_requirement(self):
        """Test Universal Entity ID Type conditional requirement."""
        dataset = Dataset()
        dataset.ContainerIdentifier = "SLIDE_001"
        dataset.ContainerTypeCodeSequence = []
        
        # Create valid specimen description
        spec_item = Dataset()
        spec_item.SpecimenIdentifier = "SPEC_001"
        spec_item.SpecimenUID = "*******.*******.9.10"
        dataset.SpecimenDescriptionSequence = [spec_item]
        
        # Create hierarchic designator with UniversalEntityID but missing Type
        issuer = Dataset()
        issuer.UniversalEntityID = "*******.5"
        # Missing UniversalEntityIDType
        dataset.IssuerOfContainerIdentifierSequence = [issuer]
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = SpecimenValidator.validate(dataset, config)
        
        type_errors = [error for error in result.errors if "Universal Entity ID Type (0040,0033) is required" in error]
        assert len(type_errors) == 1
        assert "Type 1C" in type_errors[0]
    
    def test_duplicate_specimen_identifiers_generates_error(self):
        """Test validation of duplicate specimen identifiers."""
        dataset = Dataset()
        dataset.ContainerIdentifier = "SLIDE_001"
        dataset.IssuerOfContainerIdentifierSequence = []
        dataset.ContainerTypeCodeSequence = []
        
        # Create specimens with duplicate identifiers
        spec_item1 = Dataset()
        spec_item1.SpecimenIdentifier = "SPEC_001"  # Duplicate
        spec_item1.SpecimenUID = "*******.*******.9.10"
        
        spec_item2 = Dataset()
        spec_item2.SpecimenIdentifier = "SPEC_001"  # Duplicate
        spec_item2.SpecimenUID = "*******.*******.9.11"
        
        dataset.SpecimenDescriptionSequence = [spec_item1, spec_item2]
        
        result = SpecimenValidator.validate(dataset)
        
        duplicate_errors = [error for error in result.errors if "Duplicate Specimen Identifiers detected" in error]
        assert len(duplicate_errors) == 1
        assert "unique identifier for workflow management" in duplicate_errors[0]
    
    def test_duplicate_specimen_uids_generates_error(self):
        """Test validation of duplicate specimen UIDs."""
        dataset = Dataset()
        dataset.ContainerIdentifier = "SLIDE_001"
        dataset.IssuerOfContainerIdentifierSequence = []
        dataset.ContainerTypeCodeSequence = []
        
        # Create specimens with duplicate UIDs
        spec_item1 = Dataset()
        spec_item1.SpecimenIdentifier = "SPEC_001"
        spec_item1.SpecimenUID = "*******.*******.9.10"  # Duplicate
        
        spec_item2 = Dataset()
        spec_item2.SpecimenIdentifier = "SPEC_002"
        spec_item2.SpecimenUID = "*******.*******.9.10"  # Duplicate
        
        dataset.SpecimenDescriptionSequence = [spec_item1, spec_item2]
        
        result = SpecimenValidator.validate(dataset)
        
        duplicate_uid_errors = [error for error in result.errors if "Duplicate Specimen UIDs detected" in error]
        assert len(duplicate_uid_errors) == 1
        assert "globally unique" in duplicate_uid_errors[0]
    
    def test_validation_config_options(self):
        """Test different validation configuration options."""
        # Create dataset with multiple validation issues
        dataset = Dataset()
        dataset.ContainerIdentifier = "SLIDE_001"
        dataset.IssuerOfContainerIdentifierSequence = []
        dataset.ContainerTypeCodeSequence = []
        
        spec_item = Dataset()
        spec_item.SpecimenIdentifier = "SPEC_001"
        spec_item.SpecimenUID = "*******.*******.9.10"
        dataset.SpecimenDescriptionSequence = [spec_item]
        
        # Add container component with invalid material
        component = Dataset()
        component.ContainerComponentTypeCodeSequence = [Dataset()]
        component.ContainerComponentMaterial = "INVALID"
        dataset.ContainerComponentSequence = [component]
        
        # Test with minimal validation
        config_minimal = ValidationConfig(
            validate_conditional_requirements=False,
            check_enumerated_values=False,
            validate_sequences=False
        )
        result_minimal = SpecimenValidator.validate(dataset, config_minimal)
        
        # Test with comprehensive validation
        config_full = ValidationConfig(
            validate_conditional_requirements=True,
            check_enumerated_values=True,
            validate_sequences=True
        )
        result_full = SpecimenValidator.validate(dataset, config_full)
        
        # Full validation should catch more issues
        assert len(result_full.errors) >= len(result_minimal.errors)
    
    def test_granular_validation_methods_with_dataset(self):
        """Test granular validation methods work with Dataset objects."""
        # Create valid dataset using module, then extract dataset
        specimen_desc = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10"
        )
        
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc]
        )
        
        dataset = specimen.to_dataset()
        
        # Test all granular methods with Dataset
        required_result = SpecimenValidator.validate_required_elements(dataset)
        conditional_result = SpecimenValidator.validate_conditional_requirements(dataset)
        enum_result = SpecimenValidator.validate_enumerated_values(dataset)
        sequence_result = SpecimenValidator.validate_sequence_structures(dataset)
        
        # All should return ValidationResult objects
        assert isinstance(required_result, ValidationResult)
        assert isinstance(conditional_result, ValidationResult)
        assert isinstance(enum_result, ValidationResult)
        assert isinstance(sequence_result, ValidationResult)
        
        # Valid data should pass all checks
        assert len(required_result.errors) == 0
        assert len(conditional_result.errors) == 0
        assert len(enum_result.errors) == 0
        assert len(sequence_result.errors) == 0
    
    def test_granular_validation_methods_with_basemodule(self):
        """Test granular validation methods work with BaseModule objects."""
        # Create specimen module (BaseModule instance)
        specimen_desc = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10"
        )
        
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc]
        )
        
        # Test all granular methods with BaseModule (zero-copy)
        required_result = SpecimenValidator.validate_required_elements(specimen)
        conditional_result = SpecimenValidator.validate_conditional_requirements(specimen)
        enum_result = SpecimenValidator.validate_enumerated_values(specimen)
        sequence_result = SpecimenValidator.validate_sequence_structures(specimen)
        
        # All should return ValidationResult objects
        assert isinstance(required_result, ValidationResult)
        assert isinstance(conditional_result, ValidationResult)
        assert isinstance(enum_result, ValidationResult)
        assert isinstance(sequence_result, ValidationResult)
        
        # Valid data should pass all checks
        assert len(required_result.errors) == 0
        assert len(conditional_result.errors) == 0
        assert len(enum_result.errors) == 0
        assert len(sequence_result.errors) == 0
    
    def test_validate_required_elements_with_missing_data(self):
        """Test validate_required_elements with missing required data."""
        dataset = Dataset()
        # Missing ContainerIdentifier and SpecimenDescriptionSequence
        
        result = SpecimenValidator.validate_required_elements(dataset)
        
        assert len(result.errors) >= 2
        container_errors = [e for e in result.errors if "Container Identifier" in e]
        specimen_errors = [e for e in result.errors if "Specimen Description Sequence" in e]
        
        assert len(container_errors) == 1
        assert len(specimen_errors) == 1
        assert "Type 1 element" in container_errors[0]
    
    def test_validate_conditional_requirements_multiple_specimens(self):
        """Test conditional requirements with multiple specimens (localization required)."""
        specimen_desc1 = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10"
        )
        
        specimen_desc2 = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_002",
            specimen_uid="*******.*******.9.11"
        )
        
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc1, specimen_desc2]
        )
        
        result = SpecimenValidator.validate_conditional_requirements(specimen)
        
        # Should require localization for multiple specimens
        localization_errors = [e for e in result.errors if "Specimen Localization" in e and "Type 1C" in e]
        assert len(localization_errors) == 2  # One error per specimen
        assert "multiple specimens are present" in localization_errors[0]
    
    def test_validate_enumerated_values_invalid_material(self):
        """Test enum validation with invalid container component material."""
        # Create dataset manually to avoid module validation
        dataset = Dataset()
        dataset.ContainerIdentifier = "SLIDE_001"
        
        spec_item = Dataset()
        spec_item.SpecimenIdentifier = "SPEC_001"
        spec_item.SpecimenUID = "*******.*******.9.10"
        dataset.SpecimenDescriptionSequence = [spec_item]
        
        dataset.IssuerOfContainerIdentifierSequence = []
        dataset.ContainerTypeCodeSequence = []
        
        # Create container component with invalid material
        component = Dataset()
        component.ContainerComponentTypeCodeSequence = [Dataset()]  # Minimal code sequence
        component.ContainerComponentMaterial = "INVALID_MATERIAL"  # Invalid enum
        dataset.ContainerComponentSequence = [component]
        
        result = SpecimenValidator.validate_enumerated_values(dataset)
        
        # Should detect invalid enum value
        material_warnings = [w for w in result.warnings if "Container Component Material" in w and "INVALID_MATERIAL" in w]
        assert len(material_warnings) == 1
    
    def test_validate_sequence_structures_cardinality_violations(self):
        """Test sequence structure validation with cardinality violations."""
        # Create dataset with multiple issuer items (violates 0-1 cardinality)
        dataset = Dataset()
        dataset.ContainerIdentifier = "SLIDE_001"
        
        # Create valid specimen description
        spec_item = Dataset()
        spec_item.SpecimenIdentifier = "SPEC_001"
        spec_item.SpecimenUID = "*******.*******.9.10"
        dataset.SpecimenDescriptionSequence = [spec_item]
        
        # Create multiple issuer items (violates 0-1 cardinality)
        issuer1 = Dataset()
        issuer1.LocalNamespaceEntityID = "LAB_001"
        issuer2 = Dataset()
        issuer2.LocalNamespaceEntityID = "LAB_002"
        dataset.IssuerOfContainerIdentifierSequence = [issuer1, issuer2]
        
        # Create multiple container type items (violates 0-1 cardinality)
        type1 = Dataset()
        type1.CodeValue = "433466003"
        type1.CodingSchemeDesignator = "SCT"
        type1.CodeMeaning = "Microscope slide"
        type2 = Dataset()
        type2.CodeValue = "123456789"
        type2.CodingSchemeDesignator = "TEST"
        type2.CodeMeaning = "Test container"
        dataset.ContainerTypeCodeSequence = [type1, type2]
        
        result = SpecimenValidator.validate_sequence_structures(dataset)
        
        # Should detect cardinality violations
        issuer_errors = [e for e in result.errors if "Issuer of Container Identifier Sequence" in e and "cardinality violation" in e]
        container_errors = [e for e in result.errors if "Container Type Code Sequence" in e and "cardinality violation" in e]
        
        assert len(issuer_errors) == 1
        assert len(container_errors) == 1
        assert "Zero or one Item shall be included" in issuer_errors[0]
    
    def test_validator_independence_external_dataset(self):
        """Test validator works independently with external Dataset objects."""
        # Create external dataset manually (not using module)
        external_dataset = Dataset()
        external_dataset.ContainerIdentifier = "EXTERNAL_001"
        
        # Create specimen description manually
        spec_item = Dataset()
        spec_item.SpecimenIdentifier = "EXT_SPEC_001"
        spec_item.SpecimenUID = "*******.*******.9.999"
        spec_item.IssuerOfSpecimenIdentifierSequence = []
        spec_item.SpecimenPreparationSequence = []
        
        external_dataset.SpecimenDescriptionSequence = [spec_item]
        external_dataset.IssuerOfContainerIdentifierSequence = []
        external_dataset.ContainerTypeCodeSequence = []
        
        # Validator should work with external dataset
        result = SpecimenValidator.validate(external_dataset)
        
        assert isinstance(result, ValidationResult)
        # Should pass validation
        assert len(result.errors) == 0
    
    def test_zero_copy_validation_performance(self):
        """Test that BaseModule validation uses zero-copy optimization."""
        specimen_desc = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10"
        )
        
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc]
        )
        
        # Test that passing BaseModule directly works (zero-copy)
        result_module = SpecimenValidator.validate(specimen)
        
        # Test that passing Dataset also works (for comparison)
        dataset = specimen.to_dataset()
        result_dataset = SpecimenValidator.validate(dataset)
        
        # Both should return equivalent results
        assert isinstance(result_module, ValidationResult)
        assert isinstance(result_dataset, ValidationResult)
        assert len(result_module.errors) == len(result_dataset.errors)
        assert len(result_module.warnings) == len(result_dataset.warnings)
    
    def test_error_message_quality(self):
        """Test that error messages are clear and actionable."""
        dataset = Dataset()
        # Missing all required elements
        
        result = SpecimenValidator.validate(dataset)
        
        assert len(result.errors) > 0
        for error in result.errors:
            # Check that errors include DICOM tags
            assert '(' in error and ')' in error  # DICOM tag format
            # Check that errors explain the issue
            assert any(word in error.lower() for word in ['required', 'missing', 'type 1', 'type 2', 'violation'])
            # Check that errors are helpful
            assert len(error) > 50  # Reasonably detailed
    
    def test_validation_result_structure(self):
        """Test ValidationResult object structure and methods."""
        specimen_desc = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10"
        )
        
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc]
        )
        
        dataset = specimen.to_dataset()
        result = SpecimenValidator.validate(dataset)
        
        # Test ValidationResult structure
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
        
        # Test ValidationResult methods if they exist
        if hasattr(result, 'is_valid'):
            assert isinstance(result.is_valid, bool)
        if hasattr(result, 'has_errors'):
            assert isinstance(result.has_errors, bool)
        if hasattr(result, 'has_warnings'):
            assert isinstance(result.has_warnings, bool)
    
    def test_performance_with_large_dataset(self):
        """Test validator performance with larger datasets."""
        dataset = Dataset()
        dataset.ContainerIdentifier = "SLIDE_001"
        dataset.IssuerOfContainerIdentifierSequence = []
        dataset.ContainerTypeCodeSequence = []
        
        # Create many specimen descriptions
        specimen_seq = []
        for i in range(50):  # Test with 50 specimens
            spec_item = Dataset()
            spec_item.SpecimenIdentifier = f"SPEC_{i:03d}"
            spec_item.SpecimenUID = f"*******.*******.9.{i}"
            specimen_seq.append(spec_item)
        
        dataset.SpecimenDescriptionSequence = specimen_seq
        
        # Validation should complete within reasonable time
        import time
        start_time = time.time()
        result = SpecimenValidator.validate(dataset)
        end_time = time.time()
        
        # Should complete within 5 seconds for 50 specimens
        assert (end_time - start_time) < 5.0
        assert isinstance(result, ValidationResult)
    
    def test_edge_case_empty_sequences_with_valid_data(self):
        """Test edge case with empty optional sequences but valid required data."""
        specimen_desc = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10"
        )
        
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc]
        )
        
        dataset = specimen.to_dataset()
        
        # Explicitly set optional sequences as empty
        dataset.AlternateContainerIdentifierSequence = []
        dataset.ContainerComponentSequence = []
        
        result = SpecimenValidator.validate(dataset)
        
        # Should pass validation - empty optional sequences are allowed
        assert len(result.errors) == 0