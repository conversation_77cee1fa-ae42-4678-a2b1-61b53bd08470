"""
Test RTPrescriptionValidator functionality and DICOM compliance.

Tests for DICOM PS3.3 C.8.8.10 RT Prescription Module validation.
"""

import pytest
from pydicom import Dataset
from pyrt_dicom.validators.modules.rt_prescription_validator import RTPrescriptionValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.enums.rt_enums import DoseReferenceStructureType, DoseReferenceType, DoseValuePurpose, DoseValueInterpretation
from pyrt_dicom.modules.rt_prescription_module import RTPrescriptionModule


class TestRTPrescriptionValidator:
    """Test RTPrescriptionValidator functionality and DICOM compliance."""
    
    def test_validate_returns_validation_result(self):
        """Test that validate method returns proper ValidationResult instance."""
        dataset = Dataset()
        
        result = RTPrescriptionValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
    
    def test_empty_dataset_passes_validation(self):
        """Test that empty dataset passes validation (all elements are Type 3)."""
        dataset = Dataset()
        
        result = RTPrescriptionValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_valid_prescription_description_only_passes_validation(self):
        """Test that dataset with only prescription description passes validation."""
        dataset = Dataset()
        dataset.PrescriptionDescription = "Primary treatment prescription"
        
        result = RTPrescriptionValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
    
    def test_dose_reference_sequence_missing_required_elements_fails_validation(self):
        """Test that dose reference sequence items missing required elements fail validation."""
        dataset = Dataset()
        
        # Create dose reference item missing required elements
        dose_ref_item = Dataset()
        # Missing DoseReferenceNumber, DoseReferenceStructureType, DoseReferenceType
        
        dataset.DoseReferenceSequence = [dose_ref_item]
        
        result = RTPrescriptionValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert len(result.errors) >= 3  # Should have errors for all 3 missing required elements
        
        # Check specific error messages
        error_messages = ' '.join(result.errors)
        assert "Dose Reference Number (300A,0012) is required" in error_messages
        assert "Dose Reference Structure Type (300A,0014) is required" in error_messages
        assert "Dose Reference Type (300A,0020) is required" in error_messages
    
    def test_valid_dose_reference_point_structure_passes_validation(self):
        """Test that valid dose reference with POINT structure type passes validation."""
        dataset = Dataset()
        
        dose_ref_item = Dataset()
        dose_ref_item.DoseReferenceNumber = 1
        dose_ref_item.DoseReferenceStructureType = DoseReferenceStructureType.POINT.value
        dose_ref_item.DoseReferenceType = DoseReferenceType.TARGET.value
        dose_ref_item.ReferencedROINumber = 1  # Required for POINT type
        
        dataset.DoseReferenceSequence = [dose_ref_item]
        
        result = RTPrescriptionValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
    
    def test_point_structure_missing_referenced_roi_fails_validation(self):
        """Test that POINT structure type missing Referenced ROI Number fails validation."""
        dataset = Dataset()
        
        dose_ref_item = Dataset()
        dose_ref_item.DoseReferenceNumber = 1
        dose_ref_item.DoseReferenceStructureType = DoseReferenceStructureType.POINT.value
        dose_ref_item.DoseReferenceType = DoseReferenceType.TARGET.value
        # Missing ReferencedROINumber - required for POINT type
        
        dataset.DoseReferenceSequence = [dose_ref_item]
        
        result = RTPrescriptionValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Referenced ROI Number (3006,0084) is required" in error for error in result.errors)
    
    def test_volume_structure_missing_referenced_roi_fails_validation(self):
        """Test that VOLUME structure type missing Referenced ROI Number fails validation."""
        dataset = Dataset()
        
        dose_ref_item = Dataset()
        dose_ref_item.DoseReferenceNumber = 1
        dose_ref_item.DoseReferenceStructureType = DoseReferenceStructureType.VOLUME.value
        dose_ref_item.DoseReferenceType = DoseReferenceType.ORGAN_AT_RISK.value
        # Missing ReferencedROINumber - required for VOLUME type
        
        dataset.DoseReferenceSequence = [dose_ref_item]
        
        result = RTPrescriptionValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Referenced ROI Number (3006,0084) is required" in error for error in result.errors)
    
    def test_coordinates_structure_missing_coordinates_fails_validation(self):
        """Test that COORDINATES structure type missing coordinates fails validation."""
        dataset = Dataset()
        
        dose_ref_item = Dataset()
        dose_ref_item.DoseReferenceNumber = 1
        dose_ref_item.DoseReferenceStructureType = DoseReferenceStructureType.COORDINATES.value
        dose_ref_item.DoseReferenceType = DoseReferenceType.TARGET.value
        # Missing DoseReferencePointCoordinates - required for COORDINATES type
        
        dataset.DoseReferenceSequence = [dose_ref_item]
        
        result = RTPrescriptionValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Dose Reference Point Coordinates (300A,0018) is required" in error for error in result.errors)
    
    def test_valid_coordinates_structure_passes_validation(self):
        """Test that valid dose reference with COORDINATES structure type passes validation."""
        dataset = Dataset()
        
        dose_ref_item = Dataset()
        dose_ref_item.DoseReferenceNumber = 1
        dose_ref_item.DoseReferenceStructureType = DoseReferenceStructureType.COORDINATES.value
        dose_ref_item.DoseReferenceType = DoseReferenceType.TARGET.value
        dose_ref_item.DoseReferencePointCoordinates = [100.0, 200.0, 300.0]  # Required for COORDINATES type
        
        dataset.DoseReferenceSequence = [dose_ref_item]
        
        result = RTPrescriptionValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
    
    def test_invalid_coordinates_length_fails_validation(self):
        """Test that coordinates with wrong length fails validation."""
        dataset = Dataset()
        
        dose_ref_item = Dataset()
        dose_ref_item.DoseReferenceNumber = 1
        dose_ref_item.DoseReferenceStructureType = DoseReferenceStructureType.COORDINATES.value
        dose_ref_item.DoseReferenceType = DoseReferenceType.TARGET.value
        dose_ref_item.DoseReferencePointCoordinates = [100.0, 200.0]  # Should have 3 values
        
        dataset.DoseReferenceSequence = [dose_ref_item]
        
        result = RTPrescriptionValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("must contain exactly 3 values" in error for error in result.errors)
    
    def test_duplicate_dose_reference_numbers_fails_validation(self):
        """Test that duplicate dose reference numbers fail validation."""
        dataset = Dataset()
        
        dose_ref_item1 = Dataset()
        dose_ref_item1.DoseReferenceNumber = 1
        dose_ref_item1.DoseReferenceStructureType = DoseReferenceStructureType.POINT.value
        dose_ref_item1.DoseReferenceType = DoseReferenceType.TARGET.value
        dose_ref_item1.ReferencedROINumber = 1
        
        dose_ref_item2 = Dataset()
        dose_ref_item2.DoseReferenceNumber = 1  # Duplicate number
        dose_ref_item2.DoseReferenceStructureType = DoseReferenceStructureType.VOLUME.value
        dose_ref_item2.DoseReferenceType = DoseReferenceType.ORGAN_AT_RISK.value
        dose_ref_item2.ReferencedROINumber = 2
        
        dataset.DoseReferenceSequence = [dose_ref_item1, dose_ref_item2]
        
        result = RTPrescriptionValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("must be unique within the RT Plan" in error for error in result.errors)
    
    def test_invalid_enumerated_values_generates_warnings(self):
        """Test that invalid enumerated values generate warnings."""
        dataset = Dataset()
        
        dose_ref_item = Dataset()
        dose_ref_item.DoseReferenceNumber = 1
        dose_ref_item.DoseReferenceStructureType = "INVALID_TYPE"  # Invalid enum value
        dose_ref_item.DoseReferenceType = "INVALID_REF_TYPE"      # Invalid enum value
        dose_ref_item.DoseValuePurpose = "INVALID_PURPOSE"        # Invalid enum value
        dose_ref_item.DoseValueInterpretation = "INVALID_INTERP"  # Invalid enum value
        
        dataset.DoseReferenceSequence = [dose_ref_item]
        
        result = RTPrescriptionValidator.validate(dataset)
        
        assert result.has_warnings
        # Should have warnings for invalid enum values
        warning_text = ' '.join(result.warnings)
        assert "Dose Reference Structure Type" in warning_text
        assert "Dose Reference Type" in warning_text
    
    def test_invalid_dose_value_ranges_generate_warnings(self):
        """Test that invalid dose value ranges generate warnings."""
        dataset = Dataset()
        
        dose_ref_item = Dataset()
        dose_ref_item.DoseReferenceNumber = 1
        dose_ref_item.DoseReferenceStructureType = DoseReferenceStructureType.POINT.value
        dose_ref_item.DoseReferenceType = DoseReferenceType.TARGET.value
        dose_ref_item.ReferencedROINumber = 1
        
        # Invalid dose ordering: min > prescription > max
        dose_ref_item.TargetMinimumDose = 300.0
        dose_ref_item.TargetPrescriptionDose = 200.0  
        dose_ref_item.TargetMaximumDose = 100.0
        
        # Invalid fraction values
        dose_ref_item.TargetUnderdoseVolumeFraction = 150.0  # Should be 0-100
        
        dataset.DoseReferenceSequence = [dose_ref_item]
        
        result = RTPrescriptionValidator.validate(dataset)
        
        assert result.has_warnings
        warning_text = ' '.join(result.warnings)
        assert "should not exceed" in warning_text
        assert "between 0.0 and 100.0 percent" in warning_text
    
    def test_invalid_uid_format_generates_warnings(self):
        """Test that invalid UID format generates warnings."""
        dataset = Dataset()
        
        dose_ref_item = Dataset()
        dose_ref_item.DoseReferenceNumber = 1
        dose_ref_item.DoseReferenceStructureType = DoseReferenceStructureType.POINT.value
        dose_ref_item.DoseReferenceType = DoseReferenceType.TARGET.value
        dose_ref_item.ReferencedROINumber = 1
        dose_ref_item.DoseReferenceUID = "invalid-uid-format!"  # Invalid UID
        
        dataset.DoseReferenceSequence = [dose_ref_item]
        
        result = RTPrescriptionValidator.validate(dataset)
        
        assert result.has_errors  # UID validation generates errors, not warnings
        error_text = ' '.join(result.errors)
        assert "invalid characters" in error_text
    
    def test_negative_dose_values_generate_warnings(self):
        """Test that negative dose values generate warnings."""
        dataset = Dataset()
        
        dose_ref_item = Dataset()
        dose_ref_item.DoseReferenceNumber = 1
        dose_ref_item.DoseReferenceStructureType = DoseReferenceStructureType.POINT.value
        dose_ref_item.DoseReferenceType = DoseReferenceType.TARGET.value
        dose_ref_item.ReferencedROINumber = 1
        dose_ref_item.ConstraintWeight = -1.0      # Should be non-negative
        dose_ref_item.NominalPriorDose = -5.0     # Should be non-negative
        
        dataset.DoseReferenceSequence = [dose_ref_item]
        
        result = RTPrescriptionValidator.validate(dataset)
        
        assert result.has_warnings
        warning_text = ' '.join(result.warnings)
        assert "should be non-negative" in warning_text
    
    def test_validation_config_affects_validation(self):
        """Test that ValidationConfig settings affect validation behavior."""
        dataset = Dataset()
        
        dose_ref_item = Dataset()
        dose_ref_item.DoseReferenceNumber = 1
        dose_ref_item.DoseReferenceStructureType = "INVALID_TYPE"  # Invalid enum
        dose_ref_item.DoseReferenceType = DoseReferenceType.TARGET.value
        
        dataset.DoseReferenceSequence = [dose_ref_item]
        
        # Test with enumerated value checking disabled
        config_no_enum = ValidationConfig(check_enumerated_values=False)
        result_no_enum = RTPrescriptionValidator.validate(dataset, config_no_enum)
        
        # Test with enumerated value checking enabled
        config_with_enum = ValidationConfig(check_enumerated_values=True)
        result_with_enum = RTPrescriptionValidator.validate(dataset, config_with_enum)
        
        # Should have fewer warnings when enum checking is disabled
        assert len(result_no_enum.warnings) <= len(result_with_enum.warnings)
    
    def test_error_messages_contain_dicom_tag_references(self):
        """Test that error messages contain specific DICOM tag references."""
        dataset = Dataset()
        
        dose_ref_item = Dataset()
        # Missing all required elements to get multiple tag references
        
        dataset.DoseReferenceSequence = [dose_ref_item]
        
        result = RTPrescriptionValidator.validate(dataset)
        
        assert result.has_errors
        error_text = ' '.join(result.errors)
        
        # Check that DICOM tags are mentioned in error messages
        assert "(300A,0012)" in error_text  # Dose Reference Number
        assert "(300A,0014)" in error_text  # Dose Reference Structure Type
        assert "(300A,0020)" in error_text  # Dose Reference Type
    
    def test_error_messages_provide_helpful_context(self):
        """Test that error messages provide helpful context and guidance."""
        dataset = Dataset()
        
        dose_ref_item = Dataset()
        dose_ref_item.DoseReferenceNumber = 1
        dose_ref_item.DoseReferenceStructureType = DoseReferenceStructureType.POINT.value
        dose_ref_item.DoseReferenceType = DoseReferenceType.TARGET.value
        # Missing ReferencedROINumber for POINT type
        
        dataset.DoseReferenceSequence = [dose_ref_item]
        
        result = RTPrescriptionValidator.validate(dataset)
        
        assert result.has_errors
        error_text = result.errors[0]
        
        # Check that error message provides context about why the field is required
        assert "when Dose Reference Structure Type" in error_text
        assert "identifies the ROI representing" in error_text
    
    # Tests for granular validation methods with both Dataset and BaseModule
    
    def test_validate_required_elements_with_dataset(self):
        """Test validate_required_elements with Dataset."""
        dataset = Dataset()
        
        # Empty dataset should pass (no required elements at top level)
        result = RTPrescriptionValidator.validate_required_elements(dataset)
        assert result.is_valid
        assert not result.has_errors
        
        # Dataset with incomplete dose reference item should fail
        dose_ref_item = Dataset()
        # Missing required elements
        dataset.DoseReferenceSequence = [dose_ref_item]
        
        result = RTPrescriptionValidator.validate_required_elements(dataset)
        assert not result.is_valid
        assert result.has_errors
        assert result.error_count >= 3
    
    def test_validate_required_elements_with_basemodule(self):
        """Test validate_required_elements with BaseModule."""
        # Empty module should pass
        module = RTPrescriptionModule.from_required_elements()
        result = RTPrescriptionValidator.validate_required_elements(module)
        assert result.is_valid
        assert not result.has_errors
        
        # Module with incomplete dose reference should fail
        incomplete_item = Dataset()
        # Missing required elements
        module.with_optional_elements(dose_reference_sequence=[incomplete_item])
        
        result = RTPrescriptionValidator.validate_required_elements(module)
        assert not result.is_valid
        assert result.has_errors
        assert result.error_count >= 3
    
    def test_validate_conditional_requirements_with_dataset(self):
        """Test validate_conditional_requirements with Dataset."""
        dataset = Dataset()
        
        # POINT type missing Referenced ROI Number
        dose_ref_item = Dataset()
        dose_ref_item.DoseReferenceNumber = 1
        dose_ref_item.DoseReferenceStructureType = "POINT"
        dose_ref_item.DoseReferenceType = "TARGET"
        # Missing ReferencedROINumber
        dataset.DoseReferenceSequence = [dose_ref_item]
        
        result = RTPrescriptionValidator.validate_conditional_requirements(dataset)
        assert not result.is_valid
        assert result.has_errors
        assert any("Referenced ROI Number (3006,0084) is required" in error for error in result.errors)
    
    def test_validate_conditional_requirements_with_basemodule(self):
        """Test validate_conditional_requirements with BaseModule."""
        # COORDINATES type missing coordinates
        item = Dataset()
        item.DoseReferenceNumber = 1
        item.DoseReferenceStructureType = "COORDINATES"
        item.DoseReferenceType = "TARGET"
        # Missing DoseReferencePointCoordinates
        
        module = RTPrescriptionModule.from_required_elements().with_optional_elements(
            dose_reference_sequence=[item]
        )
        
        result = RTPrescriptionValidator.validate_conditional_requirements(module)
        assert not result.is_valid
        assert result.has_errors
        assert any("Dose Reference Point Coordinates (300A,0018) is required" in error for error in result.errors)
    
    def test_validate_enumerated_values_with_dataset(self):
        """Test validate_enumerated_values with Dataset."""
        dataset = Dataset()
        
        dose_ref_item = Dataset()
        dose_ref_item.DoseReferenceNumber = 1
        dose_ref_item.DoseReferenceStructureType = "INVALID_TYPE"  # Invalid enum
        dose_ref_item.DoseReferenceType = "INVALID_REF_TYPE"      # Invalid enum
        dataset.DoseReferenceSequence = [dose_ref_item]
        
        result = RTPrescriptionValidator.validate_enumerated_values(dataset)
        assert result.has_warnings
        warning_text = ' '.join(result.warnings)
        assert "Dose Reference Structure Type" in warning_text
        assert "Dose Reference Type" in warning_text
    
    def test_validate_enumerated_values_with_basemodule(self):
        """Test validate_enumerated_values with BaseModule."""
        # Create module with valid enum values - should pass
        dose_ref_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=1,
            dose_reference_structure_type=DoseReferenceStructureType.POINT,
            dose_reference_type=DoseReferenceType.TARGET,
            referenced_roi_number=1,
            dose_value_purpose=DoseValuePurpose.TRACKING
        )
        
        module = RTPrescriptionModule.from_required_elements().with_optional_elements(
            dose_reference_sequence=[dose_ref_item]
        )
        
        result = RTPrescriptionValidator.validate_enumerated_values(module)
        assert not result.has_warnings
        assert result.is_valid
    
    def test_validate_sequence_structures_with_dataset(self):
        """Test validate_sequence_structures with Dataset."""
        dataset = Dataset()
        
        # Duplicate dose reference numbers
        item1 = Dataset()
        item1.DoseReferenceNumber = 1
        item1.DoseReferenceStructureType = "POINT"
        item1.DoseReferenceType = "TARGET"
        item1.ReferencedROINumber = 1
        
        item2 = Dataset()
        item2.DoseReferenceNumber = 1  # Duplicate
        item2.DoseReferenceStructureType = "VOLUME"
        item2.DoseReferenceType = "ORGAN_AT_RISK"
        item2.ReferencedROINumber = 2
        
        dataset.DoseReferenceSequence = [item1, item2]
        
        result = RTPrescriptionValidator.validate_sequence_structures(dataset)
        assert not result.is_valid
        assert result.has_errors
        assert any("must be unique within the RT Plan" in error for error in result.errors)
    
    def test_validate_sequence_structures_with_basemodule(self):
        """Test validate_sequence_structures with BaseModule."""
        # Invalid coordinates length
        item = Dataset()
        item.DoseReferenceNumber = 1
        item.DoseReferenceStructureType = "COORDINATES"
        item.DoseReferenceType = "TARGET"
        item.DoseReferencePointCoordinates = [100.0, 200.0]  # Should have 3 values
        
        module = RTPrescriptionModule.from_required_elements().with_optional_elements(
            dose_reference_sequence=[item]
        )
        
        result = RTPrescriptionValidator.validate_sequence_structures(module)
        assert not result.is_valid
        assert result.has_errors
        assert any("must contain exactly 3 values" in error for error in result.errors)
    
    def test_validate_dose_value_consistency_with_dataset(self):
        """Test validate_dose_value_consistency with Dataset."""
        dataset = Dataset()
        
        dose_ref_item = Dataset()
        dose_ref_item.DoseReferenceNumber = 1
        dose_ref_item.DoseReferenceStructureType = "POINT"
        dose_ref_item.DoseReferenceType = "TARGET"
        dose_ref_item.ReferencedROINumber = 1
        # Invalid dose ordering
        dose_ref_item.TargetMinimumDose = 300.0
        dose_ref_item.TargetPrescriptionDose = 200.0
        dose_ref_item.TargetMaximumDose = 100.0
        
        dataset.DoseReferenceSequence = [dose_ref_item]
        
        result = RTPrescriptionValidator.validate_dose_value_consistency(dataset)
        assert result.has_warnings
        warning_text = ' '.join(result.warnings)
        assert "should not exceed" in warning_text
    
    def test_validate_dose_value_consistency_with_basemodule(self):
        """Test validate_dose_value_consistency with BaseModule."""
        # Invalid fraction values
        item = Dataset()
        item.DoseReferenceNumber = 1
        item.DoseReferenceStructureType = "POINT"
        item.DoseReferenceType = "TARGET"
        item.ReferencedROINumber = 1
        item.TargetUnderdoseVolumeFraction = 150.0  # Should be 0-100
        
        module = RTPrescriptionModule.from_required_elements().with_optional_elements(
            dose_reference_sequence=[item]
        )
        
        result = RTPrescriptionValidator.validate_dose_value_consistency(module)
        assert result.has_warnings
        warning_text = ' '.join(result.warnings)
        assert "between 0.0 and 100.0 percent" in warning_text
    
    def test_main_validate_method_with_dataset(self):
        """Test main validate method with Dataset."""
        dataset = Dataset()
        
        # Valid complete dataset
        dose_ref_item = Dataset()
        dose_ref_item.DoseReferenceNumber = 1
        dose_ref_item.DoseReferenceStructureType = DoseReferenceStructureType.POINT.value
        dose_ref_item.DoseReferenceType = DoseReferenceType.TARGET.value
        dose_ref_item.ReferencedROINumber = 1
        dataset.DoseReferenceSequence = [dose_ref_item]
        
        result = RTPrescriptionValidator.validate(dataset)
        assert result.is_valid
        assert not result.has_errors
    
    def test_main_validate_method_with_basemodule(self):
        """Test main validate method with BaseModule."""
        # Valid complete module
        dose_ref_item = RTPrescriptionModule.create_dose_reference_item(
            dose_reference_number=1,
            dose_reference_structure_type=DoseReferenceStructureType.POINT,
            dose_reference_type=DoseReferenceType.TARGET,
            referenced_roi_number=1
        )
        
        module = RTPrescriptionModule.from_required_elements().with_optional_elements(
            dose_reference_sequence=[dose_ref_item]
        )
        
        result = RTPrescriptionValidator.validate(module)
        assert result.is_valid
        assert not result.has_errors
    
    def test_attribute_access_patterns_dataset(self):
        """Test that validator correctly uses 'attribute' in data pattern with Dataset."""
        dataset = Dataset()
        dataset.DoseReferenceSequence = []
        
        # Should not raise AttributeError when accessing non-existent attributes
        result = RTPrescriptionValidator.validate_required_elements(dataset)
        assert result.is_valid  # Empty sequence is valid for required elements check
    
    def test_attribute_access_patterns_basemodule(self):
        """Test that validator correctly uses 'attribute' in data pattern with BaseModule."""
        module = RTPrescriptionModule.from_required_elements()
        
        # Should not raise AttributeError when accessing non-existent attributes
        result = RTPrescriptionValidator.validate_required_elements(module)
        assert result.is_valid  # Empty module is valid for required elements check
    
    def test_complex_conditional_validation_scenarios(self):
        """Test complex Type 1C conditional validation scenarios."""
        # Test POINT type with Referenced ROI Number (valid)
        point_item = Dataset()
        point_item.DoseReferenceNumber = 1
        point_item.DoseReferenceStructureType = "POINT"
        point_item.DoseReferenceType = "TARGET"
        point_item.ReferencedROINumber = 1
        
        # Test VOLUME type with Referenced ROI Number (valid)
        volume_item = Dataset()
        volume_item.DoseReferenceNumber = 2
        volume_item.DoseReferenceStructureType = "VOLUME"
        volume_item.DoseReferenceType = "ORGAN_AT_RISK"
        volume_item.ReferencedROINumber = 2
        
        # Test COORDINATES type with coordinates (valid)
        coords_item = Dataset()
        coords_item.DoseReferenceNumber = 3
        coords_item.DoseReferenceStructureType = "COORDINATES"
        coords_item.DoseReferenceType = "TARGET"
        coords_item.DoseReferencePointCoordinates = [100.0, 200.0, 300.0]
        
        # Test SITE type (no conditional requirements)
        site_item = Dataset()
        site_item.DoseReferenceNumber = 4
        site_item.DoseReferenceStructureType = "SITE"
        site_item.DoseReferenceType = "TARGET"
        
        dataset = Dataset()
        dataset.DoseReferenceSequence = [point_item, volume_item, coords_item, site_item]
        
        result = RTPrescriptionValidator.validate_conditional_requirements(dataset)
        assert result.is_valid
        assert not result.has_errors
        
        # Also test with BaseModule
        module = RTPrescriptionModule.from_required_elements().with_optional_elements(
            dose_reference_sequence=[point_item, volume_item, coords_item, site_item]
        )
        
        result = RTPrescriptionValidator.validate_conditional_requirements(module)
        assert result.is_valid
        assert not result.has_errors