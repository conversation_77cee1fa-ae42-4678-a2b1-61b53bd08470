"""
Test StructureSetModule functionality.

StructureSetModule implements DICOM PS3.3 C.8.8.5 Structure Set Module.
Defines a set of areas of significance associated with Frame of Reference and images.
"""

from datetime import datetime
from pydicom import Dataset
from pyrt_dicom.modules import StructureSetModule
from pyrt_dicom.enums.rt_enums import ROIGenerationAlgorithm
from pyrt_dicom.validators.validation_result import ValidationResult
from pyrt_dicom.validators.validation_error import ValidationError


class TestStructureSetModule:
    """Test StructureSetModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        structure_set = StructureSetModule.from_required_elements(
            structure_set_label="Primary Struct",
            structure_set_date="20240101",
            structure_set_time="120000"
        )
        
        dataset = structure_set.to_dataset()
        assert dataset.StructureSetLabel == "Primary Struct"
        assert dataset.StructureSetDate == "20240101"
        assert dataset.StructureSetTime == "120000"
    
    def test_required_elements_with_defaults(self):
        """Test creation with default/empty values for Type 2 elements."""
        structure_set = StructureSetModule.from_required_elements(
            structure_set_label="Test Struct",
            structure_set_date="",  # Type 2 - can be empty
            structure_set_time=""   # Type 2 - can be empty
        )
        
        dataset = structure_set.to_dataset()
        assert dataset.StructureSetLabel == "Test Struct"
        assert dataset.StructureSetDate == ""
        assert dataset.StructureSetTime == ""
    
    def test_datetime_formatting(self):
        """Test date and time formatting from datetime objects."""
        test_datetime = datetime(2024, 1, 15, 14, 30, 45)
        
        structure_set = StructureSetModule.from_required_elements(
            structure_set_label="Test Struct",
            structure_set_date=test_datetime,
            structure_set_time=test_datetime
        )
        
        # Verify date and time were formatted correctly
        dataset = structure_set.to_dataset()
        assert dataset.StructureSetDate == "20240115"
        assert dataset.StructureSetTime == "143045"
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        structure_set = StructureSetModule.from_required_elements(
            structure_set_label="Test Struct",
            structure_set_date="20240101",
            structure_set_time="120000"
        ).with_optional_elements(
            structure_set_name="Lung Cancer Structures",
            structure_set_description="Primary target and organs at risk",
            instance_number="1"
        )
        
        dataset = structure_set.to_dataset()
        assert hasattr(dataset, 'StructureSetName')
        assert hasattr(dataset, 'StructureSetDescription')
        assert hasattr(dataset, 'InstanceNumber')
        assert dataset.StructureSetName == "Lung Cancer Structures"
        assert dataset.StructureSetDescription == "Primary target and organs at risk"
        assert dataset.InstanceNumber == "1"
    
    def test_with_frame_of_reference(self):
        """Test adding frame of reference information."""
        structure_set = StructureSetModule.from_required_elements(
            structure_set_label="Test Struct",
            structure_set_date="20240101",
            structure_set_time="120000"
        )
        
        structure_set.with_frame_of_reference(
            frame_of_reference_uid="*******.*******.9"
        )
        
        assert structure_set.has_frame_references
        dataset = structure_set.to_dataset()
        assert hasattr(dataset, 'ReferencedFrameOfReferenceSequence')
        assert len(dataset.ReferencedFrameOfReferenceSequence) == 1
        assert dataset.ReferencedFrameOfReferenceSequence[0].FrameOfReferenceUID == "*******.*******.9"
    
    def test_create_rt_referenced_study_item(self):
        """Test creation of RT referenced study item."""
        study_item = StructureSetModule.create_rt_referenced_study_item(
            referenced_sop_class_uid="1.2.840.10008.3.1.2.3.1",
            referenced_sop_instance_uid="*******.*******.10"
        )
        
        assert hasattr(study_item, 'ReferencedSOPClassUID')
        assert hasattr(study_item, 'ReferencedSOPInstanceUID')
        assert study_item.ReferencedSOPClassUID == "1.2.840.10008.3.1.2.3.1"
        assert study_item.ReferencedSOPInstanceUID == "*******.*******.10"
    
    def test_create_structure_set_roi_item_basic(self):
        """Test creation of basic structure set ROI item."""
        roi_item = StructureSetModule.create_structure_set_roi_item(
            roi_number=1,
            referenced_frame_of_reference_uid="*******.*******.9",
            roi_name="PTV",
            roi_generation_algorithm=ROIGenerationAlgorithm.MANUAL
        )
        
        assert hasattr(roi_item, 'ROINumber')
        assert hasattr(roi_item, 'ReferencedFrameOfReferenceUID')
        assert hasattr(roi_item, 'ROIName')
        assert hasattr(roi_item, 'ROIGenerationAlgorithm')
        assert roi_item.ROINumber == 1
        assert roi_item.ReferencedFrameOfReferenceUID == "*******.*******.9"
        assert roi_item.ROIName == "PTV"
        assert roi_item.ROIGenerationAlgorithm == "MANUAL"
    
    def test_with_roi_definitions(self):
        """Test adding ROI definitions."""
        structure_set = StructureSetModule.from_required_elements(
            structure_set_label="Test Struct",
            structure_set_date="20240101",
            structure_set_time="120000"
        )
        
        roi_item = StructureSetModule.create_structure_set_roi_item(
            roi_number=1,
            referenced_frame_of_reference_uid="*******.*******.9",
            roi_name="PTV",
            roi_generation_algorithm=ROIGenerationAlgorithm.MANUAL
        )
        
        structure_set.with_roi_definitions([roi_item])
        
        assert structure_set.has_roi_definitions
        assert structure_set.roi_count == 1
        dataset = structure_set.to_dataset()
        assert hasattr(dataset, 'StructureSetROISequence')
        assert len(dataset.StructureSetROISequence) == 1
    
    def test_roi_count_property(self):
        """Test ROI count property."""
        structure_set = StructureSetModule.from_required_elements(
            structure_set_label="Test Struct",
            structure_set_date="20240101",
            structure_set_time="120000"
        )
        
        # Initially no ROIs
        assert structure_set.roi_count == 0
        assert not structure_set.has_roi_definitions
        
        # Add ROIs
        roi_items = [
            StructureSetModule.create_structure_set_roi_item(
                roi_number=1,
                referenced_frame_of_reference_uid="*******.*******.9",
                roi_name="PTV",
                roi_generation_algorithm=ROIGenerationAlgorithm.MANUAL
            ),
            StructureSetModule.create_structure_set_roi_item(
                roi_number=2,
                referenced_frame_of_reference_uid="*******.*******.9",
                roi_name="Heart",
                roi_generation_algorithm=ROIGenerationAlgorithm.MANUAL
            )
        ]
        
        structure_set.with_roi_definitions(roi_items)
        assert structure_set.roi_count == 2
        assert structure_set.has_roi_definitions
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        structure_set = StructureSetModule.from_required_elements(
            structure_set_label="Test Struct",
            structure_set_date="20240101",
            structure_set_time="120000"
        )
        
        assert hasattr(structure_set, 'validate')
        assert callable(structure_set.validate)
        
        # Test validation result structure
        validation_result = structure_set.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_dataset_generation(self):
        """Test that module generates valid pydicom.Dataset objects."""
        structure_set = StructureSetModule.from_required_elements(
            structure_set_label="Test Struct",
            structure_set_date="20240101",
            structure_set_time="120000"
        )
        
        dataset = structure_set.to_dataset()
        assert isinstance(dataset, Dataset)
        assert len(dataset) > 0
        assert hasattr(dataset, 'StructureSetLabel')
        assert hasattr(dataset, 'StructureSetDate')
        assert hasattr(dataset, 'StructureSetTime')
    
    def test_dataset_independence(self):
        """Test that generated datasets are independent copies."""
        structure_set = StructureSetModule.from_required_elements(
            structure_set_label="Test Struct",
            structure_set_date="20240101",
            structure_set_time="120000"
        )
        
        dataset1 = structure_set.to_dataset()
        dataset2 = structure_set.to_dataset()
        
        # Datasets should be separate objects
        assert dataset1 is not dataset2
        # But should have the same content
        assert dataset1.StructureSetLabel == dataset2.StructureSetLabel
    
    def test_optional_elements_none_handling(self):
        """Test that None values are properly ignored for optional elements."""
        structure_set = StructureSetModule.from_required_elements(
            structure_set_label="Test Struct",
            structure_set_date="20240101",
            structure_set_time="120000"
        ).with_optional_elements(
            structure_set_name="Test Name",
            structure_set_description=None,  # Should be ignored
            instance_number="1"
        )
        
        dataset = structure_set.to_dataset()
        assert hasattr(dataset, 'StructureSetName')
        assert not hasattr(dataset, 'StructureSetDescription')
        assert hasattr(dataset, 'InstanceNumber')
        assert dataset.StructureSetName == "Test Name"
        assert dataset.InstanceNumber == "1"
    
    def test_create_contour_image_item(self):
        """Test creation of contour image item."""
        contour_image = StructureSetModule.create_contour_image_item(
            referenced_sop_class_uid="1.2.840.10008.*******.1.2",
            referenced_sop_instance_uid="*******.*******.11"
        )
        
        assert hasattr(contour_image, 'ReferencedSOPClassUID')
        assert hasattr(contour_image, 'ReferencedSOPInstanceUID')
        assert contour_image.ReferencedSOPClassUID == "1.2.840.10008.*******.1.2"
        assert contour_image.ReferencedSOPInstanceUID == "*******.*******.11"
    
    def test_create_rt_referenced_series_item(self):
        """Test creation of RT referenced series item."""
        contour_images = [
            StructureSetModule.create_contour_image_item(
                referenced_sop_class_uid="1.2.840.10008.*******.1.2",
                referenced_sop_instance_uid="*******.*******.11"
            )
        ]
        
        series_item = StructureSetModule.create_rt_referenced_series_item(
            series_instance_uid="*******.*******.12",
            contour_image_sequence=contour_images
        )
        
        assert hasattr(series_item, 'SeriesInstanceUID')
        assert hasattr(series_item, 'ContourImageSequence')
        assert series_item.SeriesInstanceUID == "*******.*******.12"
        assert len(series_item.ContourImageSequence) == 1

    def test_check_required_elements_success(self):
        """Test check_required_elements method with valid data."""
        structure_set = StructureSetModule.from_required_elements(
            structure_set_label="Test Struct",
            structure_set_date="20240101",
            structure_set_time="120000"
        )

        result = structure_set.check_required_elements()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0

    def test_check_required_elements_failure(self):
        """Test check_required_elements method with missing required elements."""
        structure_set = StructureSetModule()

        result = structure_set.check_required_elements()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) > 0
        assert any("Structure Set Label" in error for error in result.errors)

    def test_check_conditional_requirements_success(self):
        """Test check_conditional_requirements method."""
        structure_set = StructureSetModule.from_required_elements(
            structure_set_label="Test Struct",
            structure_set_date="20240101",
            structure_set_time="120000"
        )

        result = structure_set.check_conditional_requirements()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_check_enum_constraints_success(self):
        """Test check_enum_constraints method with valid enumerated values."""
        structure_set = StructureSetModule.from_required_elements(
            structure_set_label="Test Struct",
            structure_set_date="20240101",
            structure_set_time="120000"
        )

        roi_item = StructureSetModule.create_structure_set_roi_item(
            roi_number=1,
            referenced_frame_of_reference_uid="*******.*******.9",
            roi_name="PTV",
            roi_generation_algorithm=ROIGenerationAlgorithm.MANUAL
        )
        structure_set.with_roi_definitions([roi_item])

        result = structure_set.check_enum_constraints()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_check_sequence_requirements_success(self):
        """Test check_sequence_requirements method."""
        structure_set = StructureSetModule.from_required_elements(
            structure_set_label="Test Struct",
            structure_set_date="20240101",
            structure_set_time="120000"
        )

        result = structure_set.check_sequence_requirements()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_check_cross_field_dependencies_success(self):
        """Test check_cross_field_dependencies method."""
        structure_set = StructureSetModule.from_required_elements(
            structure_set_label="Test Struct",
            structure_set_date="20240101",
            structure_set_time="120000"
        )

        result = structure_set.check_cross_field_dependencies()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_zero_copy_validation(self):
        """Test that validation methods use zero-copy optimization."""
        structure_set = StructureSetModule.from_required_elements(
            structure_set_label="Test Struct",
            structure_set_date="20240101",
            structure_set_time="120000"
        )

        # All validation methods should work with the module instance directly
        result1 = structure_set.check_required_elements()
        result2 = structure_set.check_conditional_requirements()
        result3 = structure_set.check_enum_constraints()
        result4 = structure_set.check_sequence_requirements()
        result5 = structure_set.check_cross_field_dependencies()
        result6 = structure_set.validate()

        # All should return ValidationResult objects
        for result in [result1, result2, result3, result4, result5, result6]:
            assert isinstance(result, ValidationResult)

    def test_private_validation_methods_success(self):
        """Test private validation methods with valid data."""
        structure_set = StructureSetModule.from_required_elements(
            structure_set_label="Test Struct",
            structure_set_date="20240101",
            structure_set_time="120000"
        )

        # These should not raise exceptions with valid data
        structure_set._ensure_required_elements_valid()
        structure_set._ensure_conditional_requirements_valid()
        structure_set._ensure_enum_constraints_valid()
        structure_set._ensure_sequence_requirements_valid()
        structure_set._ensure_cross_field_dependencies_valid()

    def test_private_validation_methods_failure(self):
        """Test private validation methods with invalid data."""
        structure_set = StructureSetModule()  # Missing required elements

        # This should raise ValidationError
        try:
            structure_set._ensure_required_elements_valid()
            assert False, "Expected ValidationError to be raised"
        except ValidationError as e:
            assert "Required elements validation failed" in str(e)
            assert "Structure Set Label" in str(e)
