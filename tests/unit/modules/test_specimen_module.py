"""
Test SpecimenModule functionality.

SpecimenModule implements DICOM PS3.3 C.7.6.22 Specimen Module.
Contains attributes that identify one or more Specimens being imaged.
"""

from pyrt_dicom.modules import SpecimenModule
from pyrt_dicom.enums import ContainerComponentMaterial
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.validators.validation_error import ValidationError


class TestSpecimenModule:
    """Test SpecimenModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        specimen_desc = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10"
        )
        
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc]
        )
        
        # Test dataset generation
        dataset = specimen.to_dataset()
        assert dataset.ContainerIdentifier == "SLIDE_001"
        assert hasattr(dataset, 'SpecimenDescriptionSequence')
        assert hasattr(dataset, 'IssuerOfContainerIdentifierSequence')
        assert hasattr(dataset, 'ContainerTypeCodeSequence')
        assert len(dataset.SpecimenDescriptionSequence) == 1
        
        # Type 2 elements should be empty sequences by default
        assert dataset.IssuerOfContainerIdentifierSequence == []
        assert dataset.ContainerTypeCodeSequence == []
    
    def test_builder_methods_with_type2_elements(self):
        """Test builder methods for Type 2 elements."""
        specimen_desc = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10"
        )
        
        issuer_seq = [SpecimenModule.create_hierarchic_designator_item(
            local_namespace_entity_id="LAB_001"
        )]
        
        container_type_seq = [SpecimenModule.create_code_sequence_item(
            code_value="433466003",
            coding_scheme_designator="SCT",
            code_meaning="Microscope slide"
        )]
        
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc]
        ).with_issuer_information(
            issuer_of_container_identifier_sequence=issuer_seq
        ).with_container_type(
            container_type_code_sequence=container_type_seq
        )
        
        dataset = specimen.to_dataset()
        assert dataset.ContainerIdentifier == "SLIDE_001"
        assert len(dataset.SpecimenDescriptionSequence) == 1
        assert len(dataset.IssuerOfContainerIdentifierSequence) == 1
        assert len(dataset.ContainerTypeCodeSequence) == 1
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        specimen_desc = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10"
        )
        
        alt_container = SpecimenModule.create_alternate_container_item(
            container_identifier="ALT_SLIDE_001"
        )
        
        container_component = SpecimenModule.create_container_component_item(
            container_component_type_code_sequence=[
                SpecimenModule.create_code_sequence_item(
                    code_value="12345",
                    coding_scheme_designator="TEST",
                    code_meaning="Test Component"
                )
            ],
            container_component_material=ContainerComponentMaterial.GLASS
        )
        
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc]
        ).with_optional_elements(
            container_description="Standard microscope slide",
            alternate_container_identifier_sequence=[alt_container],
            container_component_sequence=[container_component]
        )
        
        dataset = specimen.to_dataset()
        assert hasattr(dataset, 'ContainerDescription')
        assert hasattr(dataset, 'AlternateContainerIdentifierSequence')
        assert hasattr(dataset, 'ContainerComponentSequence')
        assert dataset.ContainerDescription == "Standard microscope slide"
    
    def test_specimen_description_item_creation(self):
        """Test specimen description sequence item creation."""
        item = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10",
            specimen_short_description="Test specimen",
            specimen_detailed_description="Detailed description of test specimen"
        )
        
        assert item.SpecimenIdentifier == "SPEC_001"
        assert item.SpecimenUID == "*******.*******.9.10"
        assert item.SpecimenShortDescription == "Test specimen"
        assert item.SpecimenDetailedDescription == "Detailed description of test specimen"
        assert item.IssuerOfSpecimenIdentifierSequence == []
        assert item.SpecimenPreparationSequence == []
    
    def test_code_sequence_item_creation(self):
        """Test code sequence item creation."""
        item = SpecimenModule.create_code_sequence_item(
            code_value="433466003",
            coding_scheme_designator="SCT",
            code_meaning="Microscope slide",
            coding_scheme_version="2023-01"
        )
        
        assert item.CodeValue == "433466003"
        assert item.CodingSchemeDesignator == "SCT"
        assert item.CodeMeaning == "Microscope slide"
        assert item.CodingSchemeVersion == "2023-01"
    
    def test_hierarchic_designator_item_creation(self):
        """Test hierarchic designator item creation."""
        item = SpecimenModule.create_hierarchic_designator_item(
            local_namespace_entity_id="LAB_001",
            universal_entity_id="*******.5",
            universal_entity_id_type="ISO"
        )
        
        assert item.LocalNamespaceEntityID == "LAB_001"
        assert item.UniversalEntityID == "*******.5"
        assert item.UniversalEntityIDType == "ISO"
    
    def test_container_component_item_creation(self):
        """Test container component sequence item creation."""
        component_type = [SpecimenModule.create_code_sequence_item(
            code_value="12345",
            coding_scheme_designator="TEST",
            code_meaning="Test Component"
        )]
        
        item = SpecimenModule.create_container_component_item(
            container_component_type_code_sequence=component_type,
            manufacturer="TestCorp",
            container_component_material=ContainerComponentMaterial.GLASS,
            container_component_length=25.0,
            container_component_width=75.0
        )
        
        assert item.ContainerComponentTypeCodeSequence == component_type
        assert item.Manufacturer == "TestCorp"
        assert item.ContainerComponentMaterial == "GLASS"
        assert item.ContainerComponentLength == 25.0
        assert item.ContainerComponentWidth == 75.0
    
    def test_container_component_material_enum(self):
        """Test container component material enum handling."""
        component_type = [SpecimenModule.create_code_sequence_item(
            code_value="12345",
            coding_scheme_designator="TEST",
            code_meaning="Test Component"
        )]
        
        # Test with enum value
        item = SpecimenModule.create_container_component_item(
            container_component_type_code_sequence=component_type,
            container_component_material=ContainerComponentMaterial.PLASTIC
        )
        assert item.ContainerComponentMaterial == "PLASTIC"
        
        # Test with string value
        item = SpecimenModule.create_container_component_item(
            container_component_type_code_sequence=component_type,
            container_component_material="METAL"
        )
        assert item.ContainerComponentMaterial == "METAL"
    
    def test_properties(self):
        """Test specimen module properties and conditional logic."""
        specimen_desc = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10"
        )
        
        # Test basic properties with single specimen
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc]
        )
        
        assert specimen.has_specimen_data is True
        assert specimen.specimen_count == 1
        assert specimen.has_multiple_specimens is False
        assert specimen.has_container_components is False
        assert specimen.has_alternate_identifiers is False
        assert specimen.requires_specimen_localization is False
        assert specimen.has_issuer_information is False  # Empty by default
        assert specimen.has_container_type is False  # Empty by default
        
        # Test properties with issuer information and container type
        issuer_seq = [SpecimenModule.create_hierarchic_designator_item(
            local_namespace_entity_id="LAB_001"
        )]
        
        container_type_seq = [SpecimenModule.create_code_sequence_item(
            code_value="433466003",
            coding_scheme_designator="SCT",
            code_meaning="Microscope slide"
        )]
        
        specimen_with_extras = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc]
        ).with_issuer_information(
            issuer_of_container_identifier_sequence=issuer_seq
        ).with_container_type(
            container_type_code_sequence=container_type_seq
        )
        
        assert specimen_with_extras.has_issuer_information is True
        assert specimen_with_extras.has_container_type is True
        assert specimen_with_extras.requires_specimen_localization is False  # Single specimen
        
        # Test with multiple specimens
        specimen_desc2 = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_002",
            specimen_uid="*******.*******.9.11"
        )
        
        specimen_multi = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc, specimen_desc2]
        )
        
        assert specimen_multi.specimen_count == 2
        assert specimen_multi.has_multiple_specimens is True
        assert specimen_multi.requires_specimen_localization is True  # Multiple specimens
    
    def test_dataset_generation(self):
        """Test dataset generation from module."""
        specimen_desc = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10"
        )
        
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc]
        )
        
        dataset = specimen.to_dataset()
        assert isinstance(dataset, type(specimen_desc))  # Same type as Dataset
        assert len(dataset) > 0
        assert hasattr(dataset, 'ContainerIdentifier')
        assert hasattr(dataset, 'SpecimenDescriptionSequence')
        
        # Test that dataset is a copy, not reference
        original_id = dataset.ContainerIdentifier
        dataset.ContainerIdentifier = "MODIFIED"
        new_dataset = specimen.to_dataset()
        assert new_dataset.ContainerIdentifier == original_id
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        specimen_desc = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10"
        )
        
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc]
        )
        
        # Test main validate method
        assert hasattr(specimen, 'validate')
        assert callable(specimen.validate)
        
        # Test all new validation convenience methods
        assert hasattr(specimen, 'check_required_elements')
        assert hasattr(specimen, 'check_conditional_requirements')
        assert hasattr(specimen, 'check_enum_constraints')
        assert hasattr(specimen, 'check_sequence_requirements')
        
        # Test validation result structure
        validation_result = specimen.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
        
        # Test convenience method results
        required_result = specimen.check_required_elements()
        assert isinstance(required_result, ValidationResult)
        assert isinstance(required_result.errors, list)
        assert isinstance(required_result.warnings, list)
    
    def test_sequence_cardinality_validation(self):
        """Test sequence cardinality validation in builder methods."""
        specimen_desc = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10"
        )
        
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc]
        )
        
        # Test issuer sequence cardinality (0-1 items allowed)
        issuer_seq_valid = [SpecimenModule.create_hierarchic_designator_item(
            local_namespace_entity_id="LAB_001"
        )]
        issuer_seq_invalid = [
            SpecimenModule.create_hierarchic_designator_item(local_namespace_entity_id="LAB_001"),
            SpecimenModule.create_hierarchic_designator_item(local_namespace_entity_id="LAB_002")
        ]
        
        # Valid: 1 item should work
        specimen.with_issuer_information(issuer_of_container_identifier_sequence=issuer_seq_valid)
        
        # Invalid: >1 item should raise ValueError
        try:
            specimen.with_issuer_information(issuer_of_container_identifier_sequence=issuer_seq_invalid)
            assert False, "Should have raised ValueError for >1 issuer items"
        except ValueError as e:
            assert "Zero or one Item shall be included" in str(e)
        
        # Test container type sequence cardinality (0-1 items allowed)
        container_type_invalid = [
            SpecimenModule.create_code_sequence_item("123", "TEST", "Type 1"),
            SpecimenModule.create_code_sequence_item("456", "TEST", "Type 2")
        ]
        
        try:
            specimen.with_container_type(container_type_code_sequence=container_type_invalid)
            assert False, "Should have raised ValueError for >1 container type items"
        except ValueError as e:
            assert "Zero or one Item shall be included" in str(e)
    
    def test_module_base_properties(self):
        """Test inherited BaseModule properties."""
        specimen_desc = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10"
        )
        
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc]
        )
        
        assert specimen.module_name == "SpecimenModule"
        assert specimen.has_data is True
        assert specimen.get_element_count() > 0
        assert "SpecimenModule" in str(specimen)
        assert "3 attributes" in str(specimen)
    
    def test_validation_convenience_methods(self):
        """Test public validation convenience methods."""
        specimen_desc = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10"
        )
        
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc]
        )
        
        # Test all public validation methods
        required_result = specimen.check_required_elements()
        conditional_result = specimen.check_conditional_requirements()
        enum_result = specimen.check_enum_constraints()
        sequence_result = specimen.check_sequence_requirements()
        
        # All should return ValidationResult objects
        assert isinstance(required_result, ValidationResult)
        assert isinstance(conditional_result, ValidationResult)
        assert isinstance(enum_result, ValidationResult)
        assert isinstance(sequence_result, ValidationResult)
        
        # Valid module should pass all checks
        assert len(required_result.errors) == 0
        assert len(conditional_result.errors) == 0
        assert len(enum_result.errors) == 0
        assert len(sequence_result.errors) == 0
    
    def test_validation_convenience_methods_with_invalid_data(self):
        """Test validation convenience methods with invalid data."""
        # Create specimen with missing required elements
        specimen = SpecimenModule()
        
        required_result = specimen.check_required_elements()
        
        # Should detect missing required elements
        assert len(required_result.errors) > 0
        assert any("Container Identifier" in error for error in required_result.errors)
        assert any("Specimen Description Sequence" in error for error in required_result.errors)
    
    def test_private_validation_methods_success(self):
        """Test private validation methods with valid data (should not raise)."""
        specimen_desc = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10"
        )
        
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc]
        )
        
        # These should not raise exceptions with valid data
        try:
            specimen._ensure_required_elements_valid()
            specimen._ensure_conditional_requirements_valid()
            specimen._ensure_enum_constraints_valid()
            specimen._ensure_sequence_requirements_valid()
        except ValidationError:
            assert False, "Private validation methods should not raise with valid data"
    
    def test_private_validation_methods_failure(self):
        """Test private validation methods with invalid data (should raise)."""
        # Create specimen with missing required elements
        specimen = SpecimenModule()
        
        # Should raise ValidationError for missing required elements
        try:
            specimen._ensure_required_elements_valid()
            assert False, "Should have raised ValidationError"
        except ValidationError as e:
            assert "Required elements validation failed" in str(e)
            assert "Container Identifier" in str(e) or "Specimen Description" in str(e)
    
    def test_zero_copy_validation(self):
        """Test that validation methods use zero-copy (pass self, not dataset)."""
        specimen_desc = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10"
        )
        
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc]
        )
        
        # Test main validate method
        result = specimen.validate()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        
        # Test convenience methods
        required_result = specimen.check_required_elements()
        assert isinstance(required_result, ValidationResult)
        assert len(required_result.errors) == 0
    
    def test_conditional_validation_with_multiple_specimens(self):
        """Test conditional validation with multiple specimens (Type 1C requirement)."""
        specimen_desc1 = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10"
        )
        
        specimen_desc2 = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_002",
            specimen_uid="*******.*******.9.11"
            # Missing SpecimenLocalizationContentItemSequence
        )
        
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc1, specimen_desc2]
        )
        
        # Check conditional requirements - should detect missing localization
        conditional_result = specimen.check_conditional_requirements()
        assert len(conditional_result.errors) > 0
        assert any("Specimen Localization Content Item Sequence" in error and "Type 1C" in error 
                  for error in conditional_result.errors)
    
    def test_enum_validation_with_invalid_material(self):
        """Test enum validation with invalid container component material."""
        specimen_desc = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10"
        )
        
        # Create container component with invalid material
        invalid_component = SpecimenModule.create_container_component_item(
            container_component_type_code_sequence=[
                SpecimenModule.create_code_sequence_item(
                    code_value="12345",
                    coding_scheme_designator="TEST",
                    code_meaning="Test Component"
                )
            ],
            container_component_material="INVALID_MATERIAL"  # Not a valid enum
        )
        
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc]
        ).with_optional_elements(
            container_component_sequence=[invalid_component]
        )
        
        # Check enum constraints - should detect invalid material
        enum_result = specimen.check_enum_constraints()
        assert len(enum_result.warnings) > 0  # Usually warnings for invalid enums
        assert any("Container Component Material" in warning and "INVALID_MATERIAL" in warning 
                  for warning in enum_result.warnings)
    
    def test_sequence_validation_with_cardinality_violations(self):
        """Test sequence validation with cardinality violations."""
        specimen_desc = SpecimenModule.create_specimen_description_item(
            specimen_identifier="SPEC_001",
            specimen_uid="*******.*******.9.10"
        )
        
        specimen = SpecimenModule.from_required_elements(
            container_identifier="SLIDE_001",
            specimen_description_sequence=[specimen_desc]
        )
        
        # Create multiple issuer items manually (to bypass builder validation)
        # This tests that the validator correctly catches cardinality violations
        issuer1 = SpecimenModule.create_hierarchic_designator_item(local_namespace_entity_id="LAB_001")
        issuer2 = SpecimenModule.create_hierarchic_designator_item(local_namespace_entity_id="LAB_002")
        
        # Manually set invalid sequence (bypassing builder)
        specimen._dataset.IssuerOfContainerIdentifierSequence = [issuer1, issuer2]
        
        # Sequence validation should catch cardinality violation
        sequence_result = specimen.check_sequence_requirements()
        assert len(sequence_result.errors) > 0
        assert any("cardinality violation" in error for error in sequence_result.errors)
        assert any("Zero or one Item shall be included" in error for error in sequence_result.errors)
        
        # Also test the builder properly rejects invalid cardinality
        try:
            invalid_issuer_seq = [issuer1, issuer2]
            SpecimenModule.from_required_elements(
                container_identifier="SLIDE_001",
                specimen_description_sequence=[specimen_desc]
            ).with_issuer_information(
                issuer_of_container_identifier_sequence=invalid_issuer_seq
            )
            assert False, "Builder should have rejected invalid cardinality"
        except ValueError as e:
            assert "Zero or one Item shall be included" in str(e)