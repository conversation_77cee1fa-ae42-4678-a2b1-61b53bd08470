"""
Test SOPCommonModule (M - Mandatory) functionality.

SOPCommonModule implements DICOM PS3.3 C.12.1 SOP Common Module.
Required for all RTDoseIOD instances.
"""

import pytest
from datetime import datetime
from pydicom import Dataset
from pydicom.uid import generate_uid
from pyrt_dicom.modules import SOPCommonModule
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.enums.common_enums import (
    SyntheticData, SOPInstanceStatus, QueryRetrieveView,
    ContentQualification, LongitudinalTemporalInformationModified
)


class TestSOPCommonModule:
    """Test SOPCommonModule (M - Mandatory) functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        sop_instance_uid = generate_uid()
        sop_class_uid = "1.2.840.10008.*******.1.481.2"  # RT Dose Storage
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid=sop_class_uid,
            sop_instance_uid=sop_instance_uid
        )
        
        # Test through dataset generation - composition architecture pattern
        dataset = sop.to_dataset()
        assert dataset.SOPClassUID == sop_class_uid
        assert dataset.SOPInstanceUID == sop_instance_uid
    
    def test_rt_dose_sop_class_uid(self):
        """Test RT Dose specific SOP Class UID."""
        rt_dose_sop_class = "1.2.840.10008.*******.1.481.2"
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid=rt_dose_sop_class,
            sop_instance_uid=generate_uid()
        )
        
        dataset = sop.to_dataset()
        assert dataset.SOPClassUID == rt_dose_sop_class
    
    def test_sop_instance_uid_uniqueness(self):
        """Test that SOP Instance UIDs are unique."""
        sop_class = "1.2.840.10008.*******.1.481.2"
        
        sop1 = SOPCommonModule.from_required_elements(
            sop_class_uid=sop_class,
            sop_instance_uid=generate_uid()
        )
        sop2 = SOPCommonModule.from_required_elements(
            sop_class_uid=sop_class,
            sop_instance_uid=generate_uid()
        )
        
        dataset1 = sop1.to_dataset()
        dataset2 = sop2.to_dataset()
        assert dataset1.SOPInstanceUID != dataset2.SOPInstanceUID
    
    def test_various_sop_class_uids(self):
        """Test various RT-related SOP Class UIDs."""
        rt_sop_classes = [
            "1.2.840.10008.*******.1.481.2",   # RT Dose Storage
            "1.2.840.10008.*******.1.481.5",   # RT Plan Storage
            "1.2.840.10008.*******.1.481.3",   # RT Structure Set Storage
            "1.2.840.10008.*******.1.481.4"    # RT Beams Treatment Record Storage
        ]
        
        for sop_class in rt_sop_classes:
            sop = SOPCommonModule.from_required_elements(
                sop_class_uid=sop_class,
                sop_instance_uid=generate_uid()
            )
            dataset = sop.to_dataset()
            assert dataset.SOPClassUID == sop_class
    
    def test_with_optional_elements(self):
        """Test adding optional SOP Common elements."""
        current_time = datetime.now()
        instance_creation_date = current_time.strftime("%Y%m%d")
        instance_creation_time = current_time.strftime("%H%M%S.%f")[:-3]  # Microseconds to milliseconds
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_specific_character_set(
            "ISO_IR 100"
        ).with_optional_elements(
            instance_creation_date=instance_creation_date,
            instance_creation_time=instance_creation_time,
            instance_creator_uid=generate_uid(),
            instance_number=1,
            sop_instance_status=SOPInstanceStatus.AC
        )
        
        # Test through dataset generation - composition architecture pattern
        dataset = sop.to_dataset()
        assert hasattr(dataset, 'SpecificCharacterSet')
        assert hasattr(dataset, 'InstanceCreationDate')
        assert hasattr(dataset, 'InstanceCreationTime')
        assert hasattr(dataset, 'InstanceCreatorUID')
        assert hasattr(dataset, 'InstanceNumber')
    
    def test_instance_creation_datetime(self):
        """Test instance creation date and time handling."""
        creation_date = "20240101"
        creation_time = "120000.123"
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_optional_elements(
            instance_creation_date=creation_date,
            instance_creation_time=creation_time
        )
        
        dataset = sop.to_dataset()
        assert dataset.InstanceCreationDate == creation_date
        assert dataset.InstanceCreationTime == creation_time
    
    def test_specific_character_set_values(self):
        """Test various specific character set values."""
        character_sets = [
            "ISO_IR 100",    # Latin alphabet No. 1
            "ISO_IR 101",    # Latin alphabet No. 2
            "ISO_IR 109",    # Latin alphabet No. 3
            "ISO_IR 110",    # Latin alphabet No. 4
            "ISO_IR 144",    # Cyrillic
            "ISO_IR 127",    # Arabic
            "ISO_IR 126",    # Greek
            "ISO_IR 138",    # Hebrew
            "ISO_IR 148",    # Latin alphabet No. 5
            "ISO_IR 192"     # UTF-8
        ]
        
        for charset in character_sets:
            sop = SOPCommonModule.from_required_elements(
                sop_class_uid="1.2.840.10008.*******.1.481.2",
                sop_instance_uid=generate_uid()
            ).with_specific_character_set(
                charset
            )
            dataset = sop.to_dataset()
            assert dataset.SpecificCharacterSet == charset
    
    def test_instance_number_formats(self):
        """Test various instance number formats."""
        # DICOM Instance Number (IS VR) must be valid integers
        instance_numbers = [1, 123, 999]
        
        for instance_num in instance_numbers:
            sop = SOPCommonModule.from_required_elements(
                sop_class_uid="1.2.840.10008.*******.1.481.2",
                sop_instance_uid=generate_uid()
            ).with_optional_elements(
                instance_number=instance_num
            )
            dataset = sop.to_dataset()
            assert dataset.InstanceNumber == instance_num
    
    def test_instance_creator_uid_validation(self):
        """Test instance creator UID validation."""
        creator_uid = generate_uid()
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_optional_elements(
            instance_creator_uid=creator_uid
        )
        
        dataset = sop.to_dataset()
        assert dataset.InstanceCreatorUID == creator_uid
    
    def test_sop_authorization_elements(self):
        """Test SOP authorization related elements."""
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_optional_elements(
            sop_authorization_comment="Authorized for clinical use",
            authorization_equipment_certification_number="CERT123456"
        )
        
        dataset = sop.to_dataset()
        assert hasattr(dataset, 'SOPAuthorizationComment')
        assert hasattr(dataset, 'AuthorizationEquipmentCertificationNumber')
    
    def test_timezone_offset_from_utc(self):
        """Test timezone offset from UTC handling."""
        timezone_offsets = ["+0000", "-0500", "+0900", "+0530", "-0800"]
        
        for offset in timezone_offsets:
            sop = SOPCommonModule.from_required_elements(
                sop_class_uid="1.2.840.10008.*******.1.481.2",
                sop_instance_uid=generate_uid()
            ).with_optional_elements(
                timezone_offset_from_utc=offset
            )
            dataset = sop.to_dataset()
            assert dataset.TimezoneOffsetFromUTC == offset
    
    def test_digital_signatures_elements(self):
        """Test digital signature related elements."""
        # Digital signatures are not implemented in the current SOPCommonModule
        # This test verifies the module can be created and validated without these elements
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        )
        
        # Verify basic SOP Common elements are present through dataset
        dataset = sop.to_dataset()
        assert hasattr(dataset, 'SOPClassUID')
        assert hasattr(dataset, 'SOPInstanceUID')
        
        # Validate the module
        result = sop.validate()
        assert result is not None
    
    def test_rt_dose_sop_requirements(self):
        """Test RT Dose specific SOP Common requirements."""
        rt_dose_sop_class = "1.2.840.10008.*******.1.481.2"
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid=rt_dose_sop_class,
            sop_instance_uid=generate_uid()
        )
        
        # Verify RT Dose specific SOP requirements through dataset
        dataset = sop.to_dataset()
        assert dataset.SOPClassUID == rt_dose_sop_class
        assert dataset.SOPInstanceUID is not None
        assert len(dataset.SOPInstanceUID) > 0
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        )
        
        assert hasattr(sop, 'validate')
        assert callable(sop.validate)
        
        # Test validation result structure
        validation_result = sop.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_sop_class_uid_validation(self):
        """Test SOP Class UID format validation."""
        # Test valid UID format
        valid_uid = "1.2.840.10008.*******.1.481.2"
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid=valid_uid,
            sop_instance_uid=generate_uid()
        )
        
        dataset = sop.to_dataset()
        assert dataset.SOPClassUID == valid_uid
    
    def test_sop_instance_uid_format(self):
        """Test SOP Instance UID format requirements."""
        # Generate UID and verify it follows DICOM UID rules
        instance_uid = generate_uid()
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=instance_uid
        )
        
        # Verify UID characteristics through dataset
        dataset = sop.to_dataset()
        assert dataset.SOPInstanceUID == instance_uid
        assert "." in dataset.SOPInstanceUID  # UIDs contain dots
        assert len(dataset.SOPInstanceUID) <= 64  # Max UID length

    def test_conditional_query_retrieve_view(self):
        """Test conditional query/retrieve view (Type 1C)."""
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_query_retrieve_view(QueryRetrieveView.ENHANCED)
        
        dataset = sop.to_dataset()
        assert hasattr(dataset, 'QueryRetrieveView')
        assert dataset.QueryRetrieveView == QueryRetrieveView.ENHANCED.value
        assert sop.is_converted_instance  # Property test

    def test_conditional_encrypted_attributes(self):
        """Test conditional encrypted attributes sequence (Type 1C)."""
        # Create a dummy encrypted attributes item
        encrypted_item = Dataset()
        encrypted_item.EncryptedContentTransferSyntaxUID = "1.2.840.10008.1.2"
        encrypted_item.EncryptedContent = b"dummy_encrypted_content"
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_encrypted_attributes_sequence([encrypted_item])
        
        dataset = sop.to_dataset()
        assert hasattr(dataset, 'EncryptedAttributesSequence')
        assert len(dataset.EncryptedAttributesSequence) == 1
        assert sop.is_encrypted  # Property test

    def test_conditional_conversion_source(self):
        """Test conditional conversion source attributes sequence (Type 1C)."""
        # Create a dummy conversion source item
        conversion_item = Dataset()
        conversion_item.ReferencedSOPClassUID = "1.2.840.10008.*******.1.2"
        conversion_item.ReferencedSOPInstanceUID = generate_uid()
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_conversion_source_attributes_sequence([conversion_item])
        
        dataset = sop.to_dataset()
        assert hasattr(dataset, 'ConversionSourceAttributesSequence')
        assert len(dataset.ConversionSourceAttributesSequence) == 1
        assert sop.is_converted_instance  # Property test

    def test_conditional_hl7_document_reference(self):
        """Test conditional HL7 structured document reference sequence (Type 1C)."""
        # Create a dummy HL7 document reference item
        hl7_item = Dataset()
        hl7_item.ReferencedSOPClassUID = "2.16.840.1.113883.1.7.2"  # CDA Release 2
        hl7_item.ReferencedSOPInstanceUID = generate_uid()
        hl7_item.HL7InstanceIdentifier = "1.2.3.4.5^extension"
        
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_hl7_structured_document_reference_sequence([hl7_item])
        
        dataset = sop.to_dataset()
        assert hasattr(dataset, 'HL7StructuredDocumentReferenceSequence')
        assert len(dataset.HL7StructuredDocumentReferenceSequence) == 1
        assert sop.has_hl7_documents  # Property test

    def test_enhanced_optional_elements(self):
        """Test enhanced optional elements (Type 3)."""
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_optional_elements(
            synthetic_data=SyntheticData.YES,
            longitudinal_temporal_information_modified=LongitudinalTemporalInformationModified.MODIFIED,
            content_qualification=ContentQualification.RESEARCH,
            instance_origin_status="LOCAL",
            barcode_value="ABC123456"
        )
        
        dataset = sop.to_dataset()
        assert hasattr(dataset, 'SyntheticData')
        assert dataset.SyntheticData == SyntheticData.YES.value
        assert hasattr(dataset, 'LongitudinalTemporalInformationModified')
        assert dataset.LongitudinalTemporalInformationModified == LongitudinalTemporalInformationModified.MODIFIED.value
        assert hasattr(dataset, 'ContentQualification')
        assert dataset.ContentQualification == ContentQualification.RESEARCH.value
        assert hasattr(dataset, 'InstanceOriginStatus')
        assert dataset.InstanceOriginStatus == "LOCAL"
        assert hasattr(dataset, 'BarcodeValue')
        assert dataset.BarcodeValue == "ABC123456"
        
        # Test properties
        assert sop.is_synthetic

    def test_helper_methods_coding_scheme_identification(self):
        """Test coding scheme identification sequence item creation."""
        item = SOPCommonModule.create_coding_scheme_identification_item(
            coding_scheme_designator="DCM",
            coding_scheme_registry="HL7",
            coding_scheme_uid="1.2.840.10008.2.16.4",
            coding_scheme_name="DICOM Controlled Terminology"
        )
        
        assert isinstance(item, Dataset)
        assert item.CodingSchemeDesignator == "DCM"
        assert item.CodingSchemeRegistry == "HL7"
        assert item.CodingSchemeUID == "1.2.840.10008.2.16.4"
        assert item.CodingSchemeName == "DICOM Controlled Terminology"

    def test_helper_methods_context_group_identification(self):
        """Test context group identification sequence item creation."""
        item = SOPCommonModule.create_context_group_identification_item(
            context_identifier="CID_100",
            mapping_resource="DCMR",
            context_group_version="20240101",
            context_uid="1.2.3.4.5"
        )
        
        assert isinstance(item, Dataset)
        assert item.ContextIdentifier == "CID_100"
        assert item.MappingResource == "DCMR"
        assert item.ContextGroupVersion == "20240101"
        assert item.ContextUID == "1.2.3.4.5"

    def test_helper_methods_private_data_characteristics(self):
        """Test private data element characteristics sequence item creation."""
        item = SOPCommonModule.create_private_data_element_characteristics_item(
            private_group_reference=0x0011,
            private_creator_reference="MY_PRIVATE_CREATOR",
            block_identifying_information_status="SAFE"
        )
        
        assert isinstance(item, Dataset)
        assert item.PrivateGroupReference == 0x0011
        assert item.PrivateCreatorReference == "MY_PRIVATE_CREATOR"
        assert item.BlockIdentifyingInformationStatus == "SAFE"

    def test_dataset_generation_and_integrity(self):
        """Test dataset generation preserves all data."""
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_optional_elements(
            instance_creation_date="20240101",
            instance_creation_time="120000",
            synthetic_data=SyntheticData.NO,
            instance_number=1
        ).with_specific_character_set("ISO_IR 100")
        
        dataset = sop.to_dataset()
        
        # Verify all elements are preserved
        assert isinstance(dataset, Dataset)
        assert hasattr(dataset, 'SOPClassUID')
        assert hasattr(dataset, 'SOPInstanceUID')
        assert hasattr(dataset, 'InstanceCreationDate')
        assert hasattr(dataset, 'InstanceCreationTime')
        assert hasattr(dataset, 'SyntheticData')
        assert hasattr(dataset, 'InstanceNumber')
        assert hasattr(dataset, 'SpecificCharacterSet')
        
        # Verify values
        assert dataset.SyntheticData == "NO"
        assert dataset.InstanceNumber == 1
        assert dataset.SpecificCharacterSet == "ISO_IR 100"

    def test_module_properties(self):
        """Test module properties with various combinations."""
        # Test empty module
        sop_empty = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        )
        
        assert not sop_empty.has_creation_info
        assert not sop_empty.is_synthetic
        assert not sop_empty.has_authorization_info
        assert not sop_empty.is_encrypted
        assert not sop_empty.has_character_set
        assert not sop_empty.is_converted_instance
        assert not sop_empty.has_hl7_documents
        assert not sop_empty.has_private_data_characteristics
        assert not sop_empty.has_contributing_equipment
        
        # Test populated module
        contrib_item = Dataset()
        contrib_item.PurposeOfReferenceCodeSequence = []
        contrib_item.Manufacturer = "Test Manufacturer"
        
        sop_full = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_optional_elements(
            instance_creation_date="20240101",
            synthetic_data=SyntheticData.YES,
            contributing_equipment_sequence=[contrib_item]
        ).with_specific_character_set("ISO_IR 100")
        
        assert sop_full.has_creation_info
        assert sop_full.is_synthetic
        assert sop_full.has_character_set
        assert sop_full.has_contributing_equipment

    def test_check_required_elements_success(self):
        """Test check_required_elements method with valid data."""
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        )

        result = sop.check_required_elements()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0

    def test_check_required_elements_missing_sop_class_uid(self):
        """Test check_required_elements method with missing SOP Class UID."""
        sop = SOPCommonModule()
        sop.SOPInstanceUID = generate_uid()
        # Missing SOPClassUID

        result = sop.check_required_elements()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 1
        assert "SOP Class UID (0008,0016) is required (Type 1)" in result.errors[0]

    def test_check_conditional_requirements_success(self):
        """Test check_conditional_requirements method with valid data."""
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_specific_character_set("ISO_IR 100")

        result = sop.check_conditional_requirements()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0

    def test_check_conditional_requirements_character_set_warning(self):
        """Test check_conditional_requirements method with non-ASCII characters."""
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        )
        # Add non-ASCII character without character set declaration
        # Note: PatientName is not part of SOP Common Module, but we can add it for testing
        sop._dataset.PatientName = "Müller^Hans"

        result = sop.check_conditional_requirements()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) >= 1
        assert any("Specific Character Set" in warning for warning in result.warnings)

    def test_check_enum_constraints_success(self):
        """Test check_enum_constraints method with valid enumerated values."""
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_optional_elements(
            synthetic_data=SyntheticData.NO,
            sop_instance_status=SOPInstanceStatus.AO,
            content_qualification=ContentQualification.PRODUCT
        )

        result = sop.check_enum_constraints()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0

    def test_check_enum_constraints_invalid_value(self):
        """Test check_enum_constraints method with invalid enumerated value."""
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        )
        # Set invalid enum value directly
        sop.SyntheticData = "INVALID"

        result = sop.check_enum_constraints()
        assert isinstance(result, ValidationResult)
        assert len(result.warnings) >= 1
        assert any("Synthetic Data" in warning and "INVALID" in warning for warning in result.warnings)

    def test_check_sequence_requirements_success(self):
        """Test check_sequence_requirements method with valid sequences."""
        coding_item = SOPCommonModule.create_coding_scheme_identification_item(
            coding_scheme_designator="DCM",
            coding_scheme_registry="HL7",
            coding_scheme_uid="1.2.840.10008.2.16.4"
        )

        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_optional_elements(
            coding_scheme_identification_sequence=[coding_item]
        )

        result = sop.check_sequence_requirements()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) == 0
        assert len(result.warnings) == 0

    def test_check_sequence_requirements_missing_designator(self):
        """Test check_sequence_requirements method with missing required sequence elements."""
        # Create invalid coding item without required designator
        coding_item = Dataset()
        coding_item.CodingSchemeRegistry = "HL7"
        # Missing CodingSchemeDesignator

        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_optional_elements(
            coding_scheme_identification_sequence=[coding_item]
        )

        result = sop.check_sequence_requirements()
        assert isinstance(result, ValidationResult)
        assert len(result.errors) >= 1
        assert any("Coding Scheme Designator (0008,0102) is required (Type 1)" in error for error in result.errors)

    def test_private_validation_methods_success(self):
        """Test private validation methods with valid data."""
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        )

        # These should not raise exceptions with valid data
        sop._ensure_required_elements_valid()
        sop._ensure_conditional_requirements_valid()
        sop._ensure_enum_constraints_valid()
        sop._ensure_sequence_requirements_valid()

    def test_private_validation_methods_raise_exceptions(self):
        """Test private validation methods raise ValidationError on invalid data."""
        from pyrt_dicom.validators.validation_error import ValidationError

        # Test missing required elements
        sop_invalid = SOPCommonModule()
        # Missing both required elements

        with pytest.raises(ValidationError) as exc_info:
            sop_invalid._ensure_required_elements_valid()
        assert "Required elements validation failed" in str(exc_info.value)

        # Test invalid enum constraints
        sop_enum = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        )
        sop_enum.SyntheticData = "INVALID"

        # Note: Enum validation typically produces warnings, not errors
        # So this test verifies the method works without raising exceptions
        sop_enum._ensure_enum_constraints_valid()  # Should not raise

    def test_zero_copy_validation_optimization(self):
        """Test that validation methods use zero-copy optimization (pass self, not dataset)."""
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        )

        # All validation methods should work directly on the module instance
        # This tests the zero-copy optimization where self is passed instead of self.to_dataset()
        result1 = sop.check_required_elements()
        result2 = sop.check_conditional_requirements()
        result3 = sop.check_enum_constraints()
        result4 = sop.check_sequence_requirements()
        result5 = sop.validate()

        # All should return ValidationResult instances
        for result in [result1, result2, result3, result4, result5]:
            assert isinstance(result, ValidationResult)

    def test_type_1c_specific_character_set_scenarios(self):
        """Test Type 1C Specific Character Set conditional validation scenarios."""
        # Scenario 1: ASCII characters only - no character set needed
        sop_ascii = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        )
        sop_ascii._dataset.PatientName = "Smith^John"

        result = sop_ascii.check_conditional_requirements()
        assert len(result.warnings) == 0  # No warning for ASCII-only

        # Scenario 2: Non-ASCII characters without character set - should warn
        sop_non_ascii = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        )
        sop_non_ascii._dataset.PatientName = "Müller^Hans"  # Contains umlaut

        result = sop_non_ascii.check_conditional_requirements()
        assert len(result.warnings) >= 1
        assert any("Specific Character Set" in warning for warning in result.warnings)

        # Scenario 3: Non-ASCII characters with character set - should pass
        sop_with_charset = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_specific_character_set("ISO_IR 100")
        sop_with_charset._dataset.PatientName = "Müller^Hans"

        result = sop_with_charset.check_conditional_requirements()
        # Should not warn about character set since it's properly declared
        charset_warnings = [w for w in result.warnings if "Specific Character Set" in w]
        assert len(charset_warnings) == 0

    def test_type_1c_query_retrieve_view_scenarios(self):
        """Test Type 1C Query/Retrieve View conditional validation scenarios."""
        # Scenario 1: Conversion source present but no query/retrieve view - should warn
        conversion_item = Dataset()
        conversion_item.ReferencedSOPClassUID = "1.2.840.10008.*******.1.2"
        conversion_item.ReferencedSOPInstanceUID = generate_uid()

        sop_conversion = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_conversion_source_attributes_sequence([conversion_item])

        result = sop_conversion.check_conditional_requirements()
        assert len(result.warnings) >= 1
        assert any("Query/Retrieve View" in warning for warning in result.warnings)

        # Scenario 2: Query/retrieve view present but no conversion source - should warn
        sop_query_view = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_query_retrieve_view(QueryRetrieveView.ENHANCED)

        result = sop_query_view.check_conditional_requirements()
        assert len(result.warnings) >= 1
        assert any("Conversion Source Attributes Sequence" in warning for warning in result.warnings)

    def test_type_1c_encrypted_attributes_scenarios(self):
        """Test Type 1C Encrypted Attributes Sequence conditional validation scenarios."""
        # Scenario 1: Valid encrypted attributes sequence
        encrypted_item = Dataset()
        encrypted_item.EncryptedContentTransferSyntaxUID = "1.2.840.10008.1.2.1"
        encrypted_item.EncryptedContent = b"encrypted_data_here"

        sop_encrypted = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_encrypted_attributes_sequence([encrypted_item])

        result = sop_encrypted.check_conditional_requirements()
        assert len(result.errors) == 0  # Valid encrypted attributes should pass
        assert sop_encrypted.is_encrypted  # Property test

        # Scenario 2: Invalid encrypted attributes sequence (missing required elements)
        invalid_encrypted_item = Dataset()
        # Missing both EncryptedContentTransferSyntaxUID and EncryptedContent

        sop_invalid_encrypted = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_encrypted_attributes_sequence([invalid_encrypted_item])

        result = sop_invalid_encrypted.check_conditional_requirements()
        assert len(result.errors) >= 2  # Should have errors for missing required elements
        error_messages = ' '.join(result.errors)
        assert "Encrypted Content Transfer Syntax UID" in error_messages
        assert "Encrypted Content" in error_messages

    def test_type_1c_hl7_document_reference_scenarios(self):
        """Test Type 1C HL7 Structured Document Reference Sequence conditional validation scenarios."""
        # Scenario 1: Valid HL7 document reference sequence
        hl7_item = Dataset()
        hl7_item.ReferencedSOPClassUID = "2.16.840.1.113883.1.7.2"  # CDA Release 2
        hl7_item.ReferencedSOPInstanceUID = generate_uid()
        hl7_item.HL7InstanceIdentifier = "1.2.3.4.5^extension"

        sop_hl7 = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_hl7_structured_document_reference_sequence([hl7_item])

        result = sop_hl7.check_sequence_requirements()
        assert len(result.errors) == 0  # Valid HL7 reference should pass
        assert sop_hl7.has_hl7_documents  # Property test

        # Scenario 2: Invalid HL7 document reference sequence (missing required elements)
        invalid_hl7_item = Dataset()
        # Missing HL7InstanceIdentifier
        invalid_hl7_item.HL7DocumentEffectiveTime = "20240101120000"

        sop_invalid_hl7 = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_hl7_structured_document_reference_sequence([invalid_hl7_item])

        result = sop_invalid_hl7.check_sequence_requirements()
        assert len(result.errors) >= 1
        assert any("HL7 Instance Identifier (0040,E001) is required (Type 1)" in error for error in result.errors)

    def test_comprehensive_validation_integration(self):
        """Test comprehensive validation with all validation types enabled."""
        # Create a comprehensive SOP Common module with various elements
        coding_item = SOPCommonModule.create_coding_scheme_identification_item(
            coding_scheme_designator="DCM",
            coding_scheme_registry="HL7",
            coding_scheme_uid="1.2.840.10008.2.16.4"
        )

        context_item = SOPCommonModule.create_context_group_identification_item(
            context_identifier="CID_100",
            mapping_resource="DCMR",
            context_group_version="20240101"
        )

        # Create a valid purpose of reference code sequence item
        purpose_code_item = Dataset()
        purpose_code_item.CodeValue = "121320"
        purpose_code_item.CodingSchemeDesignator = "DCM"
        purpose_code_item.CodeMeaning = "Uncompressed predecessor"

        contrib_item = SOPCommonModule.create_contributing_equipment_item(
            purpose_of_reference_code_sequence=[purpose_code_item],
            manufacturer="Test Manufacturer"
        )

        sop_comprehensive = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_specific_character_set("ISO_IR 100").with_optional_elements(
            instance_creation_date="20240101",
            instance_creation_time="120000",
            synthetic_data=SyntheticData.NO,
            sop_instance_status=SOPInstanceStatus.AO,
            content_qualification=ContentQualification.PRODUCT,
            coding_scheme_identification_sequence=[coding_item],
            context_group_identification_sequence=[context_item],
            contributing_equipment_sequence=[contrib_item]
        )

        # Test all validation methods
        result_required = sop_comprehensive.check_required_elements()
        result_conditional = sop_comprehensive.check_conditional_requirements()
        result_enum = sop_comprehensive.check_enum_constraints()
        result_sequence = sop_comprehensive.check_sequence_requirements()
        result_full = sop_comprehensive.validate()

        # All should pass with no errors
        for result in [result_required, result_conditional, result_enum, result_sequence, result_full]:
            assert isinstance(result, ValidationResult)
            assert len(result.errors) == 0

    def test_validation_method_consistency(self):
        """Test that individual validation methods are consistent with full validation."""
        sop = SOPCommonModule.from_required_elements(
            sop_class_uid="1.2.840.10008.*******.1.481.2",
            sop_instance_uid=generate_uid()
        ).with_optional_elements(
            synthetic_data=SyntheticData.YES
        )

        # Individual validation results
        result_required = sop.check_required_elements()
        result_conditional = sop.check_conditional_requirements()
        result_enum = sop.check_enum_constraints()
        result_sequence = sop.check_sequence_requirements()

        # Full validation with all options enabled
        from pyrt_dicom.validators.modules.base_validator import ValidationConfig
        config = ValidationConfig(
            validate_conditional_requirements=True,
            check_enumerated_values=True,
            validate_sequences=True
        )
        result_full = sop.validate(config)

        # The sum of individual validation errors should match full validation
        total_individual_errors = (
            len(result_required.errors) +
            len(result_conditional.errors) +
            len(result_enum.errors) +
            len(result_sequence.errors)
        )

        assert total_individual_errors == len(result_full.errors)

        # The sum of individual validation warnings should match full validation
        total_individual_warnings = (
            len(result_required.warnings) +
            len(result_conditional.warnings) +
            len(result_enum.warnings) +
            len(result_sequence.warnings)
        )

        assert total_individual_warnings == len(result_full.warnings)