# Validation Integration Refactor Guide

## Overview

This document describes the refactoring process to integrate granular validation capabilities from validator classes into module classes while maintaining clear separation of concerns and following DRY principles. The refactor enables modules to provide immediate, context-aware validation feedback to users while preserving the independence of validator classes.

## Requirements

1. **Module Class Requirements**: 
- Guide users through module creation with helper functions such as `from_*`, `with_*`, and `create_*`, and useful properties such as  `is_*` and `has_*`
- Ignore module notes which indicate that validation is now handled by the *Validator class. In these cases, reimplement the validation logic in the module class based on the rules described by this markdown file.
2. **Validator Independence**: 
- Validator classes must independently validate any pydicom Dataset instance, not just those created within the current pyrt_dicom library
- Validator classes should not have any dependency on module classes
- Validator classes should import the BaseModule if TYPE_CHECKING is True
3. **Integration Capabilities**: 
- Module classes can call specific validation methods and raise exceptions at failure points so that users know exactly where the issue occurred
- Therefore, module classes will have a dependency on their corresponding validator classes
4. **Separation of Concerns**: Clear boundaries between user experience (modules) and validation logic (validators)
5. **DRY Principles**: Single source of validation logic without code duplication

## Design Pattern

### 1. Granular Validator Methods

Break down validator classes into focused, reusable static methods that accept both Dataset and BaseModule instances:

```python
from typing import Union, TYPE_CHECKING
from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule

class ExampleValidator(BaseValidator):
    """Independent validator that works on pydicom Dataset OR BaseModule instances."""
    
    @staticmethod
    def validate_required_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1 and Type 2 required elements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        result = ValidationResult()
        
        # Use 'in' operator instead of hasattr - works with both Dataset and BaseModule
        if 'ImageType' not in data:
            result.add_error("ImageType (0008,0008) is required...")
        
        # Direct attribute access - works with both types via __getattr__
        if 'PatientName' in data:
            patient_name = data.PatientName  # No getattr() needed
            
        return result
    
    @staticmethod
    def validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate Type 1C and Type 2C conditional requirements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        result = ValidationResult()
        
        # Conditional logic using direct attribute access
        if 'MultienergyCTAcquisition' in data:
            multi_energy = data.MultienergyCTAcquisition
            if multi_energy == "YES" and 'RescaleType' not in data:
                result.add_error("RescaleType required when multi-energy CT is YES")
                
        return result
    
    @staticmethod
    def validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate enumerated value constraints.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        result = ValidationResult()
        
        # Enum validation with direct access
        if 'PhotometricInterpretation' in data:
            value = data.PhotometricInterpretation
            if value not in ['MONOCHROME1', 'MONOCHROME2']:
                result.add_error(f"Invalid PhotometricInterpretation: {value}")
                
        return result
    
    @staticmethod
    def validate_sequence_structures(data: Union[Dataset, 'BaseModule']) -> ValidationResult:
        """Validate sequence structure requirements.
        
        Args:
            data: pydicom Dataset OR BaseModule instance (both support dot-style access)
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        result = ValidationResult()
        # Sequence validation logic using direct attribute access
        return result
    
    @staticmethod
    def validate(data: Union[Dataset, 'BaseModule'], config: ValidationConfig | None = None) -> ValidationResult:
        """Orchestrate all validations.
        
        Args:
            data: pydicom Dataset OR BaseModule instance for zero-copy validation
            config: Optional validation configuration
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        config = config or ValidationConfig()
        result = ValidationResult()
        
        # Always validate required elements
        result.merge(ExampleValidator.validate_required_elements(data))
        
        if config.validate_conditional_requirements:
            result.merge(ExampleValidator.validate_conditional_requirements(data))
        
        if config.check_enumerated_values:
            result.merge(ExampleValidator.validate_enumerated_values(data))
        
        if config.validate_sequences:
            result.merge(ExampleValidator.validate_sequence_structures(data))
        
        return result
```

### 2. BaseModule Integration Methods

Add **PRIVATE** validation integration helpers to BaseModule with zero-copy optimization:

```python
from typing import Callable

class BaseModule(ABC):
    """Enhanced base module with private validation integration."""
    
    def _validate_and_raise(self, 
                           validation_method: Callable[[Union[Dataset, 'BaseModule']], ValidationResult],
                           error_context: str) -> ValidationResult:
        """Execute validation and optionally raise exceptions with zero-copy optimization.
        
        Private method - not exposed to end users via IntelliSense.
        Uses self reference instead of to_dataset() for optimal performance.
        
        Args:
            validation_method: Callable[[Union[Dataset, 'BaseModule']], ValidationResult]
            error_context: str
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        result = validation_method(self)  # Zero-copy: pass self instead of self.to_dataset()
        if result.has_errors:
            raise ValidationError(f"{error_context}: {'; '.join(result.errors)}")
        return result
    
    def _check_element_validity(self, 
                               element_name: str,
                               validation_method: Callable[[Union[Dataset, 'BaseModule']], ValidationResult]) -> ValidationResult:
        """Validate specific element with context using zero-copy optimization.
        
        Private method - not exposed to end users via IntelliSense.
        
        Args:
            element_name: str
            validation_method: Callable[[Union[Dataset, 'BaseModule']], ValidationResult]
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        result = validation_method(self)  # Zero-copy: direct self reference
        if result.has_errors:
            raise ValidationError(f"Invalid {element_name}: {'; '.join(result.errors)}")
        return result
    
    def _ensure_valid_state(self) -> None:
        """Validate current state and raise exceptions for any errors.
        
        Private method - can be called by subclasses when strict validation needed.
        Uses zero-copy validation for optimal performance.
        """
        result = self.validate()  # Already optimized to use self reference
        if result.has_errors:
            raise ValidationError(f"Module validation failed: {'; '.join(result.errors)}")
```

### 3. Module Implementation Pattern

```python
class ExampleModule(BaseModule):
    """Example module with integrated validation capabilities and zero-copy optimization."""
    
    def with_optional_elements(self, multi_energy_ct_acquisition=None):
        """Add optional elements with validation integration."""
        if multi_energy_ct_acquisition is not None:
            # Direct attribute assignment - no setattr() needed
            self.MultienergyCTAcquisition = format_enum_string(multi_energy_ct_acquisition)
            
            # Optional immediate validation - private method call with zero-copy
            self._check_element_validity(
                'MultienergyCTAcquisition',
                ExampleValidator.validate_enumerated_values
            )
        
        return self
    
    # Public convenience methods for specific validation checks with zero-copy optimization
    def check_required_elements(self) -> ValidationResult:
        """Check required elements using validator with zero-copy optimization.
        
        Returns:
            ValidationResult: Validation result with errors for missing required elements
        """
        return ExampleValidator.validate_required_elements(self)  # Pass self, not self.to_dataset()
    
    def check_conditional_requirements(self) -> ValidationResult:
        """Check conditional requirements using validator with zero-copy optimization.
        
        Returns:
            ValidationResult: Validation result with errors for missing conditional requirements
        """
        return ExampleValidator.validate_conditional_requirements(self)
    
    def check_enum_constraints(self) -> ValidationResult:
        """Check enumerated value constraints with zero-copy optimization.
        
        Returns:
            ValidationResult: Validation result with errors for invalid enumerated values
        """
        return ExampleValidator.validate_enumerated_values(self)
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Full validation using validator with zero-copy optimization.
        
        Args:
            config: Optional validation configuration
        
        Returns:
            ValidationResult: Validation result with errors for invalid sequence structures
        """
        return ExampleValidator.validate(self, config)  # Direct self reference for performance
    
    # Private methods for strict validation scenarios
    def _ensure_required_elements_valid(self) -> None:
        """Ensure required elements are valid, raise exception if not.
        
        Raises:
            ValidationError: If required elements are invalid
        """
        self._validate_and_raise(
            ExampleValidator.validate_required_elements,
            "Required elements validation failed"
        )
    
    def _ensure_enum_constraints_valid(self) -> None:
        """Ensure enum constraints are valid, raise exception if not.
        
        Raises:
            ValidationError: If enum constraints are invalid
        """
        self._validate_and_raise(
            ExampleValidator.validate_enumerated_values,
            "Enumerated value validation failed"
        )
```

## Key Design Principles

### ✅ Clean IntelliSense Experience
- All validation integration methods are **private** (prefixed with `_`)
- End users only see public API methods and properties
- Public validation methods return `ValidationResult` objects for inspection

### ✅ Validator Independence with Zero-Copy Performance
- All validator methods are static and work on pydicom Dataset OR BaseModule instances  
- Validators have no dependency on module classes - they use `Union[Dataset, 'BaseModule']` type hints
- Single source of validation logic in validator classes
- Zero-copy optimization: BaseModule instances passed directly instead of creating Dataset copies
- Use `'attribute' in data` and `data.attribute` patterns instead of hasattr/getattr/setattr

### ✅ Flexible Error Handling with Zero-Copy Performance
```python
# Lenient mode (default) - ValidationResult objects with zero-copy validation
module = ExampleModule()
result = module.validate()  # Uses self reference, not self.to_dataset()
if result.has_errors:
    print("Issues found:", result.errors)

# Immediate validation check with zero-copy optimization
result = module.check_required_elements()  # Direct self reference
if result.has_errors:
    print("Required elements missing:", result.errors)

# Exception-based validation (when needed) - also zero-copy
try:
    module._ensure_required_elements_valid()  # Private method with direct validation
except ValidationError as e:
    print("Fix this:", e)

# External Dataset validation still works (maintains validator independence)
external_dataset = pydicom.dcmread("some_file.dcm")
result = ExampleValidator.validate(external_dataset)  # Still supported
```

### ✅ DRY Principles
- Single validation logic implementation in validator static methods
- Module methods delegate to validator methods
- No duplication of validation rules or logic
- Granular validator methods can be composed for different scenarios

## Implementation Checklist

### Phase 1: Foundation Setup (One-Time Infrastructure) ✅ DONE

#### A. ValidationError Exception Class Creation
- [x] Create `src/pyrt_dicom/validators/validation_error.py` with ValidationError exception class
- [x] Add `__init__.py` imports to make ValidationError available throughout the package
- [x] Test ValidationError class with basic unit tests

#### B. BaseModule Enhancement 
- [x] Add private `_validate_and_raise()` method to BaseModule with zero-copy optimization
- [x] Add private `_check_element_validity()` method to BaseModule with zero-copy optimization  
- [x] Add private `_ensure_valid_state()` method to BaseModule with zero-copy optimization
- [x] Import ValidationError in BaseModule for type hints and exception raising
- [x] Ensure all new methods are private (underscore prefix) and use `Union[Dataset, 'BaseModule']` type hints
- [x] Use modern Python type hints (`a | b`) instead of `Union[a, b]` from typing library
- [x] Update BaseModule tests to cover new private validation integration methods

#### C. Foundation Validation
- [x] Run all existing tests to ensure BaseModule changes don't break anything
- [x] Verify ValidationError can be imported and used correctly
- [x] Confirm infrastructure is ready for individual module refactoring

---

### Phase 2: Individual Module/Validator Refactoring (One Module at a Time)

**⚠️ CRITICAL**: Complete ALL steps for ONE module/validator pair before moving to the next. Each module must be fully working with all tests passing before proceeding.

#### Step 1: Choose Target Module (Priority Order)
1. **CT Image Module** (Reference implementation)
2. **Patient Module** (Complex Type 1C/2C patterns)  
3. **General Study Module** (Good Type 1/2 patterns)
4. **RT Dose Module** (Production-ready reference)
5. **Continue with remaining 40 modules...**

#### Step 2: Validator Refactoring (Single Module)
- [ ] **Target Module**: `[ModuleName]Validator` (e.g., CTImageValidator)
- [ ] Add granular validation methods with `Union[Dataset, 'BaseModule']` support:
  - [ ] `validate_required_elements(data: Union[Dataset, 'BaseModule']) -> ValidationResult`
  - [ ] `validate_conditional_requirements(data: Union[Dataset, 'BaseModule']) -> ValidationResult` 
  - [ ] `validate_enumerated_values(data: Union[Dataset, 'BaseModule']) -> ValidationResult`
  - [ ] `validate_sequence_structures(data: Union[Dataset, 'BaseModule']) -> ValidationResult`
  - [ ] Add any module-specific validation methods (e.g., `validate_pixel_data_consistency`)
- [ ] Update main `validate(data: Union[Dataset, 'BaseModule'], config)` method to orchestrate granular methods
- [ ] **Replace all** `hasattr()`, `getattr()`, `setattr()` with `'attribute' in data` and `data.attribute` patterns

#### Step 3: Module Integration (Single Module)
- [ ] **Target Module**: `[ModuleName]Module` (e.g., CTImageModule)
- [ ] Add public validation convenience methods with zero-copy optimization:
  - [ ] `check_required_elements() -> ValidationResult` → calls validator with `self`
  - [ ] `check_conditional_requirements() -> ValidationResult` → calls validator with `self`
  - [ ] `check_enum_constraints() -> ValidationResult` → calls validator with `self`
  - [ ] Add any module-specific public validation methods as their own `check_*` methods
- [ ] Update existing `validate(config) -> ValidationResult` → calls validator with `self` (zero-copy)
- [ ] Add private validation methods for exception-based validation:
  - [ ] `_ensure_required_elements_valid() -> None` → raises ValidationError on failure
  - [ ] `_ensure_conditional_requirements_valid() -> None` → raises ValidationError on failure
  - [ ] `_ensure_enum_constraints_valid() -> None` → raises ValidationError on failure
- [ ] **All methods use zero-copy**: pass `self` instead of `self.to_dataset()`

#### Step 4: Module Test Updates (Single Module)
- [ ] **Target File**: `tests/unit/modules/test_[module_name]_module.py`
- [ ] Update existing module tests to cover new public validation methods
- [ ] Add tests for zero-copy validation (verify `self` is passed to validator methods)
- [ ] Add tests for ValidationResult return values from public methods
- [ ] Add tests for private validation methods that raise ValidationError
- [ ] Test both success and failure scenarios for all new validation methods
- [ ] Ensure all existing module functionality still works

#### Step 5: Validator Test Creation/Updates (Single Module)  
- [ ] **Target File**: `tests/unit/validators/test_[module_name]_validator.py`
- [ ] **Create validator tests if they don't exist** (most don't exist yet per the overview)
- [ ] Test all granular validation methods individually:
  - [ ] `test_validate_required_elements()` with both Dataset and BaseModule
  - [ ] `test_validate_conditional_requirements()` with both Dataset and BaseModule
  - [ ] `test_validate_enumerated_values()` with both Dataset and BaseModule  
  - [ ] `test_validate_sequence_structures()` with both Dataset and BaseModule
- [ ] Test main `validate()` method orchestration with both Dataset and BaseModule
- [ ] Test validator independence (external Dataset validation)
- [ ] Test all validation failure scenarios with proper ValidationResult error messages
- [ ] Verify zero-copy works correctly (BaseModule passed without copying)

#### Step 6: Integration Testing & Validation (Single Module)
- [ ] **Run ALL tests** for the current module/validator pair:
  - [ ] `pytest tests/unit/modules/test_[module_name]_module.py -v`
  - [ ] `pytest tests/unit/validators/test_[module_name]_validator.py -v`
- [ ] **All tests must pass** before proceeding to next module
- [ ] Run broader test suite to ensure no regressions:
  - [ ] `pytest tests/unit/modules/ -v` (verify no other modules broken)
  - [ ] `pytest tests/unit/validators/ -v` (verify no other validators broken)
- [ ] **Manual verification**:
  - [ ] Create test script showing zero-copy validation works
  - [ ] Verify ValidationError exceptions raised appropriately
  - [ ] Confirm external Dataset validation still works
  - [ ] Test all public validation methods return proper ValidationResult

#### Step 7: Documentation & Completion (Single Module)
- [ ] Update module docstrings with new validation method examples
- [ ] Mark module as ✅ **COMPLETE** in Module-by-Module Status section below
- [ ] Commit changes for this single module/validator pair
- [ ] **ONLY THEN** proceed to next module in priority order

---

### Phase 3: Repeat Phase 2 for Each Remaining Module

**⚠️ CRITICAL**: Do NOT attempt to refactor multiple modules simultaneously. Each module must be completed individually with all tests passing.

**Completion Criteria for Each Module**:
1. ✅ Validator has granular methods with `Union[Dataset, 'BaseModule']` support
2. ✅ Module has public validation methods with zero-copy optimization
3. ✅ Module has private validation methods that raise ValidationError
4. ✅ All module tests updated and passing
5. ✅ All validator tests created/updated and passing
6. ✅ Integration testing confirms everything works
7. ✅ No regressions in other modules

**Only after ALL 44 modules are individually completed**, proceed to Phase 4.

---

### Phase 4: Final Integration

This phase only begins after all 44 individual modules are complete.

- [ ] **Full test suite validation**: `pytest tests/ -v` (all tests passing)
- [ ] **Documentation updates**: Update overall project documentation
- [ ] **Production readiness**: Confirm all ValidationError handling works in real scenarios

## Module-by-Module Implementation Status

### Core Modules
- ✅ approval - ApprovalValidator ⭐ *COMPLETE*
- ✅ cine - CineValidator ⭐ *COMPLETE*
- ✅ clinical_trial_series - ClinicalTrialSeriesValidator ⭐ *COMPLETE*
- ✅ clinical_trial_study - ClinicalTrialStudyValidator ⭐ *COMPLETE*
- ✅ clinical_trial_subject - ClinicalTrialSubjectValidator ⭐ *COMPLETE*
- ✅ common_instance_reference - CommonInstanceReferenceValidator ⭐ *COMPLETE*
- ✅ contrast_bolus - ContrastBolusValidator ⭐ *COMPLETE*
- ✅ ct_image - CTImageValidator ⭐ *Reference implementation COMPLETE* - Phase 2 validation integration verified August 29, 2025
- ✅ device - DeviceValidator ⭐ *COMPLETE*
- ✅ enhanced_patient_orientation - EnhancedPatientOrientationValidator ⭐ *COMPLETE*

### General Modules
- ✅ frame_extraction - FrameExtractionValidator ⭐ *COMPLETE*
- ✅ frame_of_reference - FrameOfReferenceValidator ⭐ *COMPLETE*
- ✅ general_acquisition - GeneralAcquisitionValidator ⭐ *COMPLETE*
- ✅ general_equipment - GeneralEquipmentValidator ⭐ *COMPLETE*
- ✅ general_image - GeneralImageValidator ⭐ *COMPLETE* - Phase 2 validation integration verified August 30, 2025
- ✅ general_reference - GeneralReferenceValidator ⭐ *COMPLETE* - Phase 2 validation integration verified August 30, 2025
- ✅ general_series - GeneralSeriesValidator ⭐ *COMPLETE*
- ✅ general_study - GeneralStudyValidator ⭐ *Good example for Type 1/2 patterns COMPLETE*
- ✅ image_pixel - ImagePixelValidator ⭐ *COMPLETE* - Phase 2 validation integration verified August 30, 2025
- ✅ image_plane - ImagePlaneValidator ⭐ *COMPLETE*

### Specialized Modules
- ✅ modality_lut - ModalityLutValidator ⭐ *COMPLETE* - Phase 2 validation integration verified August 30, 2025
- ✅ multi_energy_ct_image - MultiEnergyCTImageValidator ⭐ *COMPLETE* - Phase 2 validation integration verified August 30, 2025
- ✅ multi_frame - MultiFrameValidator ⭐ *COMPLETE* - Phase 2 validation integration verified August 30, 2025
- ✅ overlay_plane - OverlayPlaneValidator ⭐ *COMPLETE*
- ✅ patient - PatientValidator ⭐ *Complete*
- ✅ patient_study - PatientStudyValidator ⭐ *COMPLETE*
- ✅ roi_contour - RoiContourValidator  ⭐ *COMPLETE*
- [ ] specimen - SpecimenValidator
- ✅ structure_set - StructureSetValidator ⭐ *COMPLETE* - Phase 2 validation integration verified August 31, 2025
- [ ] synchronization - SynchronizationValidator
- [ ] voi_lut - VoiLutValidator

### RT-Specific Modules
- ✅ rt_beams - RTBeamsValidator ⭐ *COMPLETE* - Phase 2 validation integration verified August 30, 2025
- ✅ rt_brachy_application_setups - RTBrachyApplicationSetupsValidator ⭐ *COMPLETE*
- ✅ rt_dose - RTDoseValidator ⭐ *Production-ready reference COMPLETE* - Phase 2 validation integration verified August 30, 2025
- ✅ rt_dvh - RTDvhValidator ⭐ *COMPLETE* - Phase 2 validation integration verified August 30, 2025
- ✅ rt_fraction_scheme - RTFractionSchemeValidator ⭐ *COMPLETE* - Phase 2 validation integration verified August 30, 2025
- ✅ rt_general_plan - RTGeneralPlanValidator ⭐ *COMPLETE* - Phase 2 validation integration verified August 30, 2025
- ✅ rt_image - RTImageValidator ⭐ *COMPLETE* - Phase 2 validation integration verified August 30, 2025
- [ ] rt_patient_setup - RTPatientSetupValidator
- ✅ rt_prescription - RTPrescriptionValidator ⭐ *COMPLETE* - Phase 2 validation integration verified August 31, 2025
- ✅ rt_roi_observations - RTROIObservationsValidator ⭐ *COMPLETE* - Phase 2 validation integration verified August 30, 2025
- ✅ rt_series - RTSeriesValidator ⭐ *COMPLETE* - Phase 2 validation integration verified August 30, 2025
- ✅ rt_tolerance_tables - RTToleranceTablesValidator ⭐ *COMPLETE* - Phase 2 validation integration verified August 30, 2025
- ✅ sop_common - SopCommonValidator ⭐ *COMPLETE* - Phase 2 validation integration verified August 31, 2025

**Total Modules**: 44
**Status**: 40/44 Complete (90.9%)

## Benefits of This Refactor

### 🎯 **Enhanced User Experience**
- Clean IntelliSense with only relevant public methods visible
- Immediate validation feedback when needed
- Flexible error handling (ValidationResult vs exceptions)
- Context-aware error messages

### 🔧 **Improved Architecture**
- Clear separation of concerns between modules and validators
- DRY principles maintained with single source of validation logic
- Validator independence preserved for external Dataset validation
- Granular validation methods enable targeted checks
- Zero-copy performance optimization for BaseModule validation

### 🚀 **Development Benefits**
- Easier debugging with specific validation methods
- Better testing granularity
- Consistent patterns across all 44 modules
- Maintainable codebase with clear responsibilities

### 📊 **DICOM Compliance**
- Comprehensive validation coverage
- Type 1/1C/2/2C/3 requirement handling
- Complex conditional logic support
- Enumerated value constraint validation

## Future Considerations

### Performance Optimization
- Consider caching validation results for unchanged datasets
- Implement lazy validation for expensive checks
- Profile validation performance on large datasets

### Advanced Features
- Validation configuration profiles for different use cases
- Custom validation rule injection
- Validation result aggregation across multiple modules
- Integration with DICOM file validation tools

### Documentation
- Create validation cookbook with common patterns
- Document custom validation rule creation
- Provide performance tuning guidelines
- Create troubleshooting guide for validation failures

---

*This refactor maintains the production-ready architecture while significantly enhancing the developer experience and validation capabilities.*

## Implementation History
*Created 8/29/2025*
*Last updated 8/30/2025*
